# Code Owners for Zhi Deep Learning Mentor
# 语法参考：https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners
#
# 匹配规则从上到下，后者可覆盖前者。
# 使用 GitHub 用户名或团队（需在同组织）如 @org/team-name。
# 当提交影响匹配路径时，列出的 owner 将被自动请求评审。

# 全局默认所有者（至少指定一位）
*       @your-org/po @your-org/tech-leads

# 后端（FastAPI）
/apps/backend/            @your-org/backend
/apps/backend/**          @your-org/backend

# 前端（React + Vite）
/apps/frontend/           @your-org/frontend
/apps/frontend/**         @your-org/frontend

# 共享包与类型
/packages/                @your-org/arch @your-org/frontend @your-org/backend
/packages/**              @your-org/arch @your-org/frontend @your-org/backend

# 文档（产品/架构/UX）
/docs/                    @your-org/po @your-org/arch @your-org/ux
/docs/**                  @your-org/po @your-org/arch @your-org/ux

# CI/CD 与工作流
/.github/                 @your-org/devops @your-org/tech-leads
/.github/**               @your-org/devops @your-org/tech-leads

# 根级工程规范文件
/README.md                @your-org/po @your-org/tech-leads
/CONTRIBUTING.md          @your-org/po @your-org/tech-leads
/.editorconfig            @your-org/tech-leads
/.env.example             @your-org/devops @your-org/backend

# 注意：
# - 请将 @your-org/* 替换为实际的组织/团队或具体 GitHub 用户名。
# - 团队需要在同一个 GitHub 组织内且具备仓库访问权限。
# - 大型变更（跨目录）将通知多个所有者，请合理调整范围以避免评审噪声。