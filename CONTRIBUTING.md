# 贡献指南（Contributing Guide）

感谢您对“知深学习导师”项目的贡献意愿！本项目采用轻量但严格的工程化规范，以确保高效协作与可维护性。

关联文档：[`README.md`](README.md)｜[`docs/architecture.md`](docs/architecture.md)｜[`docs/prd.md`](docs/prd.md)｜[`docs/ui-ux.md`](docs/ui-ux.md)｜Epic [`docs/epic-core-mvp.md`](docs/epic-core-mvp.md)

## 分支策略
- main：稳定生产分支，仅从 PR 合并，需通过集成测试与发布检查
- develop：集成分支，合并 feature 分支，触发预览部署
- feature/*：功能开发分支，命名示例：
  - feat/session-create-api
  - fix/session-list-empty-state
  - chore/ci-add-sentry-upload
  - docs/update-readme-quickstart

## 提交信息（Conventional Commits）
格式：`<type>(scope): <subject>`

常用 type：
- feat：新功能
- fix：缺陷修复
- docs：文档
- style：代码风格（不影响功能）
- refactor：重构（不修复bug/不加功能）
- perf：性能优化
- test：测试
- build：构建系统或依赖变更
- ci：CI配置
- chore：杂项
- revert：回滚

示例：
- feat(session): add POST /api/sessions endpoint with validation
- fix(chat): handle SSE abort with retry backoff
- docs(readme): add OpenAPI reference link

## PR 规范
- 关联 Issue/Epic：在描述中链接
- 内容结构：变更动机、主要变更点、破坏性变更（如有）、测试覆盖、截图/录屏（UI）
- 勿在大型 PR 中混入风格/格式化改动
- CI 必须绿灯（Lint/Test/Build）
- 请求至少 1 名代码所有者（CODEOWNERS）审阅

## 代码风格与质量
- 前端：ESLint + Prettier（建议在IDE中开启保存自动修复）
- 后端：Ruff + Black（或 Flake8 + Black），类型建议开启 mypy（后续）
- EditorConfig：项目已提供统一配置（见 [`.editorconfig`](.editorconfig)）
- 命名：英文、语义化，避免缩写（除通用缩写）
- 错误模型：统一使用 error.code/message/details/trace_id（参考架构文档 7/12.1）

## 测试
- 前端：Vitest + React Testing Library
- 后端：Pytest
- 必须包含：关键路径单测或集成测试；错误/边界条件；基础可访问性检查（UI）
- 覆盖率：不设硬阈值，但关键路径应具备合理覆盖

## 提交流程
1. 从 `develop` 切出 feature 分支
2. 开发并保证本地通过 Lint/Test
3. 更新必要文档（README/OpenAPI/变更日志）
4. 提交符合规范的 commit 信息
5. 发起 PR → 通过 CI → 评审通过 → 合并至 develop/main

## 安全与密钥
- 不要将任何密钥、token、私钥提交到仓库
- 使用 `.env`（基于 [`.env.example`](.env.example)）
- CI 使用 GitHub Encrypted Secrets，参考《上线前准备清单》流程
- 如发现安全问题，请通过私信/安全邮箱报告，不要公开 Issue

## 设计与产品对齐
- UI/UX 以 [`docs/ui-ux.md`](docs/ui-ux.md) 为准，确保组件属性/交互一致
- API 以 [`docs/openapi.yaml`](docs/openapi.yaml) 与 [`docs/architecture.md`](docs/architecture.md) 为准
- 需求与验收以 [`docs/prd.md`](docs/prd.md) 与 Epic 为准

感谢贡献！