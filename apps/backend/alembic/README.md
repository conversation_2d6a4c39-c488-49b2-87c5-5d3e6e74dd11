# Database Migrations

This directory contains Alembic database migration scripts for the zhiread backend.

## Commands

All commands should be run from the `apps/backend/` directory.

### Generate a new migration

```bash
alembic revision --autogenerate -m "Description of changes"
```

### Apply migrations

```bash
# Apply all pending migrations
alembic upgrade head

# Apply migrations to a specific revision
alembic upgrade <revision>

# Rollback to previous revision
alembic downgrade -1

# Rollback to specific revision
alembic downgrade <revision>
```

### Check migration status

```bash
# Show current migration status
alembic current

# Show migration history
alembic history

# Show pending migrations
alembic show <revision>
```

## Migration Files

Migration files are stored in the `versions/` directory and follow the naming convention:
`YYYY_MM_DD_HHMM_<revision>_<slug>.py`

Each migration file contains:
- `upgrade()`: Function to apply the migration
- `downgrade()`: Function to rollback the migration

## Configuration

- Database URL is read from the `DATABASE_URL` environment variable
- Alembic configuration is in `alembic.ini`
- Environment setup is in `alembic/env.py`