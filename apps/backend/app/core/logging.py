import sys
from loguru import logger

from app.core.config import settings


def setup_logging():
    """Setup structured logging with <PERSON><PERSON><PERSON>."""
    
    # Remove default logger
    logger.remove()
    
    # Configure format for structured logging
    log_format = (
        "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
        "{level: <8} | "
        "{name}:{function}:{line} | "
        "{extra[trace_id]:<36} | "
        "{message}"
    )
    
    # Add console handler
    logger.add(
        sys.stderr,
        format=log_format,
        level="INFO" if settings.environment == "production" else "DEBUG",
        enqueue=True,
        diagnose=settings.app_debug,
        backtrace=settings.app_debug,
    )
    
    # Add file handler for production
    if settings.environment == "production":
        logger.add(
            "logs/app.log",
            format=log_format,
            level="INFO",
            rotation="1 day",
            retention="30 days",
            compression="gz",
            enqueue=True,
            diagnose=False,
            backtrace=False,
        )
    
    logger.info("Logging setup completed", extra={"trace_id": "system"})


def get_logger_with_trace(trace_id: str = "unknown"):
    """Get logger instance with trace_id context."""
    return logger.bind(trace_id=trace_id)