from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class AppSettings(BaseSettings):
    """Application configuration settings."""
    
    model_config = SettingsConfigDict(env_file=".env", case_sensitive=False)
    
    # Supabase Configuration
    supabase_url: str = Field(default="https://example.supabase.co")
    supabase_service_role_key: str = Field(default="your-service-role-key-here")
    
    # Database Configuration  
    database_url: str = Field(default="sqlite:///:memory:")
    
    # Sentry Configuration (Optional)
    sentry_dsn: Optional[str] = Field(default=None)
    
    # Application Configuration
    api_prefix: str = Field(default="/api")
    app_host: str = Field(default="0.0.0.0")
    app_port: int = Field(default=8000)
    app_debug: bool = Field(default=False)
    
    # Environment
    environment: str = Field(default="development")
    
    # Development Settings (Optional)
    dev_user_id: Optional[str] = Field(default="550e8400-e29b-41d4-a716-446655440000")


settings = AppSettings()