import uuid
import traceback
from typing import Any, Dict, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from loguru import logger

try:
    import sentry_sdk
    SENTRY_AVAILABLE = True
except ImportError:
    SENTRY_AVAILABLE = False


class ErrorCode:
    """Standardized error codes."""
    INVALID_INPUT = "INVALID_INPUT"
    UNAUTHORIZED = "UNAUTHORIZED" 
    FORBIDDEN = "FORBIDDEN"
    NOT_FOUND = "NOT_FOUND"
    INTERNAL_ERROR = "INTERNAL_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"


class ErrorResponse:
    """Standardized error response structure."""
    
    def __init__(
        self, 
        code: str, 
        message: str, 
        details: Optional[Dict[str, Any]] = None,
        trace_id: Optional[str] = None
    ):
        self.code = code
        self.message = message
        self.details = details or {}
        self.trace_id = trace_id
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "error": {
                "code": self.code,
                "message": self.message,
                "details": self.details,
                "trace_id": self.trace_id
            }
        }


class TraceMiddleware(BaseHTTPMiddleware):
    """Middleware to inject trace_id into requests and responses."""
    
    async def dispatch(self, request: Request, call_next):
        # Generate or extract trace_id
        trace_id = request.headers.get("X-Trace-Id") or str(uuid.uuid4())
        
        # Store trace_id in request state
        request.state.trace_id = trace_id
        
        try:
            # Process request
            response = await call_next(request)
            
            # Add trace_id to response headers
            response.headers["X-Trace-Id"] = trace_id
            
            return response
            
        except Exception as exc:
            # Handle unexpected errors
            logger.error(f"Unhandled exception: {str(exc)}", extra={"trace_id": trace_id})
            
            # Add trace_id to Sentry context if available
            if SENTRY_AVAILABLE:
                sentry_sdk.set_tag("trace_id", trace_id)
                sentry_sdk.capture_exception(exc)
            
            error_response = ErrorResponse(
                code=ErrorCode.INTERNAL_ERROR,
                message="服务器内部错误，请稍后重试。",
                details={"exception": str(exc)} if request.app.debug else {},
                trace_id=trace_id
            )
            
            return JSONResponse(
                status_code=500,
                content=error_response.to_dict(),
                headers={"X-Trace-Id": trace_id}
            )


def create_http_exception_handler():
    """Create HTTP exception handler with standardized error format."""
    
    async def http_exception_handler(request: Request, exc: HTTPException):
        trace_id = getattr(request.state, 'trace_id', str(uuid.uuid4()))
        
        # Add trace_id to Sentry context for HTTP exceptions if available
        if SENTRY_AVAILABLE and exc.status_code >= 500:
            sentry_sdk.set_tag("trace_id", trace_id)
            sentry_sdk.capture_exception(exc)
        
        # Map HTTP status codes to error codes
        status_to_code = {
            400: ErrorCode.INVALID_INPUT,
            401: ErrorCode.UNAUTHORIZED,
            403: ErrorCode.FORBIDDEN,
            404: ErrorCode.NOT_FOUND,
            500: ErrorCode.INTERNAL_ERROR,
            503: ErrorCode.SERVICE_UNAVAILABLE,
        }
        
        error_code = status_to_code.get(exc.status_code, ErrorCode.INTERNAL_ERROR)
        
        # Log HTTP exceptions with trace_id
        logger.info(f"HTTP exception {exc.status_code}: {exc.detail}", extra={"trace_id": trace_id})
        
        error_response = ErrorResponse(
            code=error_code,
            message=exc.detail,
            trace_id=trace_id
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response.to_dict(),
            headers={"X-Trace-Id": trace_id}
        )
    
    return http_exception_handler