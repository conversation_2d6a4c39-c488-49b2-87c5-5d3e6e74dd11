import uuid
from typing import Optional
from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.core.errors import ErrorCode
from app.core.config import settings


# Security scheme for JWT token
security = HTTPBearer()


class AuthService:
    """Authentication service with Supabase JWT validation (placeholder)."""
    
    def __init__(self):
        # TODO: Initialize Supabase client when needed
        pass
    
    async def verify_token(self, token: str) -> dict:
        """
        Verify JWT token with Supabase Auth.
        
        Currently returns a placeholder user for development.
        TODO: Implement actual Supabase JWT verification.
        """
        if not token:
            raise HTTPException(
                status_code=401,
                detail="认证令牌缺失。"
            )
        
        # Placeholder: Return fixed user for development
        # In production, this would verify the JWT with Supabase
        placeholder_user = {
            "user_id": "550e8400-e29b-41d4-a716-************",  # Fixed UUID for development
            "email": "<EMAIL>",
            "role": "authenticated"
        }
        
        return placeholder_user
    
    async def get_user_from_token(self, token: str) -> dict:
        """Extract user information from JWT token."""
        return await self.verify_token(token)


# Initialize auth service
auth_service = AuthService()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    """
    Dependency to get current authenticated user.
    
    Currently returns placeholder user for development.
    TODO: Enable actual JWT validation when ready.
    """
    try:
        token = credentials.credentials
        user = await auth_service.get_user_from_token(token)
        return user
    except Exception as e:
        raise HTTPException(
            status_code=401,
            detail="无效的认证令牌。"
        )


async def get_current_user_optional(
    request: Request
) -> Optional[dict]:
    """
    Optional dependency to get current user without requiring authentication.
    Useful for endpoints that can work with or without authentication.
    """
    try:
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return None
        
        token = auth_header.split(" ")[1]
        user = await auth_service.get_user_from_token(token)
        return user
    except Exception:
        return None


async def get_current_user_id(current_user: dict = Depends(get_current_user)) -> str:
    """Convenience dependency to get current user ID."""
    return current_user["user_id"]


# For development: Get user ID with fallback to placeholder
async def get_user_id_with_fallback(request: Request) -> str:
    """
    Get user ID with fallback to placeholder for development.
    This allows testing without requiring actual authentication.
    """
    user = await get_current_user_optional(request)
    if user:
        return user["user_id"]
    
    # Fallback to fixed UUID for development/testing
    return "550e8400-e29b-41d4-a716-************"