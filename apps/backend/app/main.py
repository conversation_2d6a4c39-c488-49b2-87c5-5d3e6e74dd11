import sentry_sdk
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration

from app.api.routes import router as api_router
from app.core.config import settings
from app.core.errors import TraceMiddleware, create_http_exception_handler
from app.core.logging import setup_logging, get_logger_with_trace


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan handler."""
    # Startup
    logger = get_logger_with_trace("app-startup")
    logger.info("Starting zhiread backend application")
    
    # Setup logging
    setup_logging()
    
    # Initialize Sentry if DSN is provided
    if settings.sentry_dsn:
        sentry_sdk.init(
            dsn=settings.sentry_dsn,
            integrations=[
                FastApiIntegration(auto_enabling=True),
                StarletteIntegration(auto_enabling=True),
            ],
            traces_sample_rate=0.1,
            environment=settings.environment,
            release="1.0.0",  # TODO: Get from version file
        )
        logger.info("Sentry initialized")
    
    logger.info("Application startup complete")
    
    yield
    
    # Shutdown
    logger.info("Shutting down zhiread backend application")


def create_app() -> FastAPI:
    """Create FastAPI application instance."""
    
    app = FastAPI(
        title="Zhiread Backend API",
        description="智能阅读助手后端API",
        version="1.0.0",
        lifespan=lifespan,
        debug=settings.app_debug,
        docs_url="/docs" if settings.app_debug else None,
        redoc_url="/redoc" if settings.app_debug else None,
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # TODO: Configure properly for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Add trace middleware (must be added before other middlewares)
    app.add_middleware(TraceMiddleware)
    
    # Add exception handlers
    app.add_exception_handler(HTTPException, create_http_exception_handler())
    
    # Include API routes
    app.include_router(api_router, prefix=settings.api_prefix)
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "service": "zhiread-backend",
            "version": "1.0.0"
        }
    
    return app


# Create application instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.app_host,
        port=settings.app_port,
        reload=settings.app_debug,
        log_level="info" if not settings.app_debug else "debug",
    )