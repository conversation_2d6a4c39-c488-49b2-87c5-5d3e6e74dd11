import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text, Index, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from sqlalchemy.types import TypeDecorator, Text as SQLText

from app.db.session import Base


class JSONBType(TypeDecorator):
    """
    Custom type that uses JSONB for PostgreSQL and JSON for other databases.
    Falls back to TEXT for databases that don't support JSON.
    """
    
    impl = SQLText
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(JSONB())
        elif dialect.name in ('mysql', 'sqlite'):
            return dialect.type_descriptor(JSON())
        else:
            return dialect.type_descriptor(SQLText())


class UUIDType(TypeDecorator):
    """
    Custom UUID type that works with both PostgreSQL and SQLite.
    """
    
    impl = String
    cache_ok = True
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID(as_uuid=True))
        else:
            return dialect.type_descriptor(String(36))
    
    def process_bind_param(self, value, dialect):
        """Convert UUID to string for non-PostgreSQL databases."""
        if value is None:
            return value
        if dialect.name == 'postgresql':
            return value
        else:
            # Convert UUID to string for SQLite/MySQL
            return str(value)
    
    def process_result_value(self, value, dialect):
        """Convert string back to UUID for non-PostgreSQL databases."""
        if value is None:
            return value
        if dialect.name == 'postgresql':
            return value
        else:
            # Convert string back to UUID for SQLite/MySQL
            return uuid.UUID(value) if value else None


class Session(Base):
    """Session model representing user reading sessions."""
    
    __tablename__ = "sessions"
    
    # Primary key
    id = Column(UUIDType(), primary_key=True, default=uuid.uuid4, index=True)
    
    # User reference
    user_id = Column(UUIDType(), nullable=False, index=True)
    
    # Session data
    title = Column(Text, nullable=False)
    reading_state = Column(JSONBType(), nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(
        DateTime(timezone=True), 
        nullable=False, 
        default=func.now(),
        server_default=func.now()
    )
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_sessions_user_created_at', 'user_id', 'created_at'),
    )
    
    def __repr__(self):
        return f"<Session(id={self.id}, user_id={self.user_id}, title='{self.title[:50]}...')>"