from datetime import datetime
from typing import List, Optional
from uuid import UUID
from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Any
from datetime import timezone


class SessionCreate(BaseModel):
    """Request model for creating a new session."""
    text: str = Field(..., min_length=1, max_length=50000, description="要处理的文本内容")
    
    @field_validator('text')
    @classmethod
    def validate_text_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError('文本内容不能为空')
        return v.strip()


class SessionResponse(BaseModel):
    """Response model for session data (list item/basic)."""
    # 强制 created_at 以 UTC ISO8601（Z 结尾）序列化输出
    model_config = ConfigDict(
        from_attributes=True,
        ser_json_timedelta="iso8601",
        ser_json_bytes="utf8",
        json_encoders={
            datetime: lambda dt: (
                dt.astimezone(timezone.utc).replace(microsecond=0).isoformat().replace("+00:00", "Z")
                if dt.tzinfo is not None
                else dt.replace(tzinfo=timezone.utc, microsecond=0).isoformat().replace("+00:00", "Z")
            )
        },
    )
    
    id: UUID = Field(..., description="会话唯一标识")
    title: str = Field(..., description="会话标题")
    created_at: datetime = Field(..., description="创建时间（UTC ISO8601，Z 结尾）")


class SessionListResponse(BaseModel):
    """Response model for session list."""
    model_config = ConfigDict(from_attributes=True)
    
    items: List[SessionResponse] = Field(default_factory=list, description="会话列表")
    total: int = Field(0, description="总数量")


# --- 新增用于 Reader 的详情模型 ---
class SessionParagraph(BaseModel):
    index: int = Field(..., description="段落索引（从0开始）")
    text: str = Field(..., description="段落文本")


class SessionContent(BaseModel):
    language: str = Field("en", description="内容语言")
    source: str = Field("sample", description="内容来源：upload|url|sample")
    paragraphs: List[SessionParagraph] = Field(default_factory=list, description="段落数组")


# --- 新增：会话对话消息与摘要模型（对齐架构 12.2 共享类型） ---
class Message(BaseModel):
    id: UUID = Field(..., description="消息ID")
    role: str = Field(..., description="消息角色：user|assistant")
    content: str = Field(..., description="消息内容纯文本")
    created_at: datetime = Field(..., description="创建时间（UTC ISO8601，Z 结尾）")


class Summary(BaseModel):
    id: UUID = Field(..., description="摘要ID")
    session_id: UUID = Field(..., description="会话ID")
    version: int = Field(..., description="摘要版本（递增）")
    text: str = Field(..., description="摘要文本")
    created_at: datetime = Field(..., description="创建时间（UTC ISO8601，Z 结尾）")


class SessionDetailResponse(SessionResponse):
    """
    详情响应，包含可渲染内容与断点续学所需的消息/摘要/阅读位置。
    - messages: 历史消息（按 created_at 升序）
    - summary_latest: 当前最新摘要（可为空）
    - reading_position: 阅读位置百分比（0-100）
    """
    content: SessionContent = Field(..., description="用于 Reader 渲染的最小内容")
    messages: List[Message] = Field(default_factory=list, description="会话历史消息（升序）")
    summary_latest: Optional[Summary] = Field(default=None, description="最新摘要（若存在）")
    reading_position: Optional[int] = Field(default=None, ge=0, le=100, description="阅读位置百分比（0-100）")


# --- 新增：阅读进度模型 ---
class SessionProgressResponse(BaseModel):
    """读取会话阅读进度的响应模型"""
    session_id: UUID = Field(..., description="会话ID")
    progress: int = Field(..., ge=0, le=100, description="阅读进度百分比（0-100）")
    # 注意：version 不在响应体中返回，由路由使用 ETag 头传递

class SessionProgressUpdateRequest(BaseModel):
    """更新会话阅读进度的请求模型"""
    progress: int = Field(..., ge=0, le=100, description="阅读进度百分比（0-100）")
    # If-Match 通过请求头传递，body 不带 version