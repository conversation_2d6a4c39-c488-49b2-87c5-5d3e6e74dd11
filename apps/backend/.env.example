# Supabase Configuration
# Get these from your Supabase project settings
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Database Configuration
# For local development with PostgreSQL:
DATABASE_URL=postgresql+psycopg2://user:password@localhost:5432/zhiread
# For SQLite testing (not recommended for production):
# DATABASE_URL=sqlite:///./zhiread.db

# Sentry Configuration (Optional)
# Leave empty to disable error reporting
SENTRY_DSN=

# Application Configuration
API_PREFIX=/api
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=false

# Environment
ENVIRONMENT=development

# Development Settings (Optional)
# Default user ID for development when no auth token provided
DEV_USER_ID=550e8400-e29b-41d4-a716-446655440000