#!/usr/bin/env python3

"""Debug script to test the session creation functionality."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import traceback
from uuid import UUID
from app.db.session import engine, SessionLocal
from app.db.base import Base
from app.models.session import Session as SessionModel
from app.models.schemas import SessionCreate
from app.crud.sessions import session_crud

def main():
    print("=== Zhiread Backend Debug Test ===")
    
    try:
        # Recreate all tables
        print("1. Creating tables...")
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        print("✅ Tables created successfully")
        
        # Test database session
        print("2. Testing database session...")
        db = SessionLocal()
        
        # Test CRUD operations
        print("3. Testing CRUD operations...")
        session_data = SessionCreate(text="Hello World Test")
        user_id = UUID("550e8400-e29b-41d4-a716-************")
        
        session = session_crud.create_session(
            db=db,
            session_data=session_data,
            user_id=user_id
        )
        
        print(f"✅ Session created: {session.id}, title: '{session.title}'")
        
        # Test retrieval
        sessions = session_crud.get_sessions_by_user(db=db, user_id=user_id)
        print(f"✅ Found {len(sessions)} sessions for user")
        
        db.close()
        
        print("\n=== All tests passed! ===")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())