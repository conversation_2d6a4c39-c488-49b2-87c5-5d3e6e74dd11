import pytest
import asyncio
from uuid import UUID
from httpx import Async<PERSON>lient
from fastapi.testclient import TestClient
from datetime import datetime, timezone
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.db.session import Base
from app.api.deps import get_db

# Test database setup
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={
        "check_same_thread": False,
    },
    poolclass=StaticPool,
)

TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="function")
def setup_database():
    """Setup test database."""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


class TestSessionsAPI:
    """Test cases for sessions API endpoints."""
    
    def test_health_check(self, client: TestClient):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "zhiread-backend"
    
    def test_create_session_success(self, client: TestClient, setup_database):
        """Test successful session creation."""
        payload = {"text": "这是一段测试文本，用于创建学习会话。"}
        
        response = client.post("/api/sessions", json=payload)
        
        assert response.status_code == 201
        data = response.json()
        
        # Check response structure
        assert "id" in data
        assert "title" in data
        assert "created_at" in data

        # Validate created_at is UTC ISO8601 with Z
        created_at = data["created_at"]
        assert created_at.endswith("Z")
        # parse and ensure tz-aware UTC without microseconds
        dt = datetime.fromisoformat(created_at.replace("Z", "+00:00"))
        assert dt.tzinfo is not None and dt.tzinfo.utcoffset(dt).total_seconds() == 0
        assert dt.microsecond == 0
        
        # Validate UUID format
        session_id = UUID(data["id"])
        assert str(session_id) == data["id"]
        
        # Check title generation
        assert data["title"] == "这是一段测试文本，用于创建学习会话。"
        
        # Check trace_id in response headers
        assert "X-Trace-Id" in response.headers
        assert len(response.headers["X-Trace-Id"]) == 36  # UUID length
    
    def test_create_session_empty_text(self, client: TestClient, setup_database):
        """Test session creation with empty text."""
        payload = {"text": ""}
        
        response = client.post("/api/sessions", json=payload)
        
        assert response.status_code == 422  # Validation error
    
    def test_create_session_whitespace_only(self, client: TestClient, setup_database):
        """Test session creation with whitespace only."""
        payload = {"text": "   \n\t  "}
        
        response = client.post("/api/sessions", json=payload)
        
        assert response.status_code == 422  # Validation error
    
    def test_create_session_long_text(self, client: TestClient, setup_database):
        """Test session creation with long text."""
        long_text = "这是一段很长的文本，" * 10 + "用于测试标题截断功能。"
        payload = {"text": long_text}
        
        response = client.post("/api/sessions", json=payload)
        
        assert response.status_code == 201
        data = response.json()
        
        # Check that title is truncated
        assert len(data["title"]) <= 53  # 50 chars + "..."
        assert data["title"].endswith("...")
    
    def test_create_session_multiline_text(self, client: TestClient, setup_database):
        """Test session creation with multiline text."""
        payload = {"text": "第一行标题\n第二行内容\n第三行更多内容"}
        
        response = client.post("/api/sessions", json=payload)
        
        assert response.status_code == 201
        data = response.json()
        
        # Should use first line as title
        assert data["title"] == "第一行标题"
    
    def test_get_sessions_empty(self, client: TestClient, setup_database):
        """Test getting sessions when none exist."""
        response = client.get("/api/sessions")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["items"] == []
        assert data["total"] == 0
    
    def test_get_sessions_with_data(self, client: TestClient, setup_database):
        """Test getting sessions after creating some."""
        # Create a few sessions
        sessions_data = [
            {"text": "第一个会话"},
            {"text": "第二个会话"},
            {"text": "第三个会话"},
        ]
        
        created_sessions = []
        for session_data in sessions_data:
            response = client.post("/api/sessions", json=session_data)
            assert response.status_code == 201
            created_sessions.append(response.json())
        
        # Get sessions list
        response = client.get("/api/sessions")
        assert response.status_code == 200
        data = response.json()
        
        assert len(data["items"]) == 3
        assert data["total"] == 3
        
        # Check ordering (newest first)
        assert data["items"][0]["title"] == "第三个会话"
        assert data["items"][2]["title"] == "第一个会话"
    
    def test_get_session_by_id(self, client: TestClient, setup_database):
        """Test getting a specific session by ID."""
        # Create a session first
        payload = {"text": "测试会话内容"}
        create_response = client.post("/api/sessions", json=payload)
        assert create_response.status_code == 201
        session_id = create_response.json()["id"]
        
        # Get the session by ID
        response = client.get(f"/api/sessions/{session_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == session_id
        assert data["title"] == "测试会话内容"
    
    def test_get_nonexistent_session(self, client: TestClient, setup_database):
        """Test getting a session that doesn't exist."""
        fake_id = "550e8400-e29b-41d4-a716-446655440001"
        
        response = client.get(f"/api/sessions/{fake_id}")
        assert response.status_code == 404
    
    def test_error_response_format(self, client: TestClient, setup_database):
        """Test that error responses follow the standardized format."""
        response = client.post("/api/sessions", json={"text": ""})
        
        assert response.status_code == 422
        assert "X-Trace-Id" in response.headers
    
    def test_trace_id_consistency(self, client: TestClient, setup_database):
        """Test that trace_id is consistent in headers and error response."""
        # Send request with custom trace_id
        custom_trace_id = "test-trace-id-12345"
        headers = {"X-Trace-Id": custom_trace_id}
        
        response = client.get("/api/sessions", headers=headers)
        
        # Should return the same trace_id in response headers
        assert response.headers.get("X-Trace-Id") == custom_trace_id


if __name__ == "__main__":
    pytest.main([__file__, "-v"])