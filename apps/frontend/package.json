{"name": "zhiread-frontend", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{ts,tsx}\"", "test": "vitest run"}, "dependencies": {"react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.7.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitejs/plugin-react-swc": "^3.7.0", "eslint": "^8.57.0", "eslint-plugin": "^1.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.2.0", "jsdom": "^26.1.0", "typescript": "^5.4.5", "vite": "^5.4.0", "vitest": "^1.6.0"}}