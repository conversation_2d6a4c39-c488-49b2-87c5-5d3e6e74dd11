import React from "react";
import { buildReportMailto, errorTexts, longLoadingMs } from "../constants/ui";

// Skeleton 基础条
function Bar({ width = "100%", height = 12, radius = 6 }: { width?: number | string; height?: number; radius?: number }) {
  return (
    <div
      style={{
        width,
        height,
        borderRadius: radius,
        background:
          "linear-gradient(90deg, rgba(229,231,235,0.9) 25%, rgba(243,244,246,0.9) 37%, rgba(229,231,235,0.9) 63%)",
        backgroundSize: "400% 100%",
        animation: "zhiread-skeleton-shimmer 1.4s ease-in-out infinite",
      }}
      data-testid="skeleton-bar"
    />
  );
}

// Library 列表骨架：6–10 条
export function SkeletonList({ count = 8 }: { count?: number }) {
  const n = Math.max(6, Math.min(10, count));
  return (
    <div aria-live="polite" role="status" style={{ display: "flex", flexDirection: "column", gap: 12 }} data-testid="placeholder-loading">
      {Array.from({ length: n }).map((_, i) => (
        <div key={i} style={{ border: "1px solid #eee", borderRadius: 8, padding: 12 }}>
          <Bar width="50%" height={14} />
          <div style={{ height: 8 }} />
          <Bar width="30%" height={10} />
        </div>
      ))}
      <style>
        {`@keyframes zhiread-skeleton-shimmer{0%{background-position:100% 50%}100%{background-position:0 50%}}`}
      </style>
    </div>
  );
}

// Reader 段落骨架：3–6 段
export function SkeletonReader({ paragraphs = 4 }: { paragraphs?: number }) {
  const n = Math.max(3, Math.min(6, paragraphs));
  const [longLoading, setLongLoading] = React.useState(false);

  React.useEffect(() => {
    const t = setTimeout(() => setLongLoading(true), longLoadingMs);
    return () => clearTimeout(t);
  }, []);

  return (
    <div
      aria-live="polite"
      role="status"
      aria-busy="true"
      aria-label={longLoading ? errorTexts.aria.longLoading : undefined}
      style={{ display: "flex", flexDirection: "column", gap: 16 }}
      data-testid="placeholder-loading"
    >
      {Array.from({ length: n }).map((_, i) => (
        <div key={i} style={{ display: "flex", flexDirection: "column", gap: 8 }}>
          <Bar width="90%" />
          <Bar width="95%" />
          <Bar width="70%" />
        </div>
      ))}
      <style>
        {`@keyframes zhiread-skeleton-shimmer{0%{background-position:100% 50%}100%{background-position:0 50%}}`}
      </style>
      {longLoading && (
        <div style={{ marginTop: 8, color: "#6b7280", fontSize: 12 }} aria-live="polite" data-testid="loading-long-hint">
          {errorTexts.aria.longLoading}
        </div>
      )}
    </div>
  );
}

// Session 详情头部骨架
export function SkeletonSessionHeader() {
  return (
    <div aria-live="polite" role="status" aria-busy="true" style={{ display: "flex", flexDirection: "column", gap: 8, marginBottom: 12 }} data-testid="placeholder-loading">
      <Bar width="20%" height={10} />
      <Bar width="60%" height={16} />
      <Bar width="30%" height={10} />
      <style>
        {`@keyframes zhiread-skeleton-shimmer{0%{background-position:100% 50%}100%{background-position:0 50%}}`}
      </style>
    </div>
  );
}

export function EmptyState({
  title,
  subtitle,
  ctaText,
  onCta,
}: {
  title: string;
  subtitle?: string;
  ctaText?: string;
  onCta?: () => void;
}) {
  return (
    <div
      role="status"
      aria-live="polite"
      style={{
        border: "1px dashed #e5e7eb",
        background: "#fafafa",
        padding: 24,
        borderRadius: 8,
        textAlign: "center",
      }}
      data-testid="placeholder-empty"
    >
      <div style={{ fontWeight: 600, marginBottom: 6 }}>{title}</div>
      {subtitle && <div style={{ color: "#667085", fontSize: 13, marginBottom: 12 }}>{subtitle}</div>}
      {ctaText && onCta && (
        <button onClick={onCta} aria-label={ctaText} style={{ padding: "6px 12px", borderRadius: 6 }}>
          {ctaText}
        </button>
      )}
    </div>
  );
}

type ErrorType = "network" | "timeout" | "unauthorized" | "notfound" | "server" | "unknown";

export function ErrorState({
  traceId,
  onRetry,
  onReport,
  extra,
  page,
  errorType = "unknown",
  primaryAction,
  secondaryAction,
}: {
  traceId?: string;
  onRetry?: () => void;
  onReport?: () => void;
  extra?: React.ReactNode;
  page?: string;
  errorType?: ErrorType;
  primaryAction?: { text: string; onClick: () => void };
  secondaryAction?: { text: string; onClick: () => void };
}) {
  // 诊断输出
  React.useEffect(() => {
    // eslint-disable-next-line no-console
    console.info("[ErrorState]", {
      page: page || "unknown",
      trace_id: traceId,
      path: location.pathname + location.search,
      retryable: !!onRetry,
      errorType,
    });
  }, [traceId, onRetry, page, errorType]);

  const report = () => {
    if (onReport) return onReport();
    const url = buildReportMailto(traceId);
    window.location.href = url;
  };

  const dict = (errorTexts.types as any)[errorType] || errorTexts.types.unknown;

  return (
    <div
      role="alert"
      aria-live="assertive"
      aria-label={errorTexts.aria.errorRegionLabel}
      style={{
        border: "1px solid #fecaca",
        background: "#fff1f2",
        padding: 16,
        borderRadius: 8,
      }}
      data-testid="placeholder-error"
    >
      <div style={{ fontWeight: 600, color: "#991b1b", marginBottom: 4 }}>{dict.title || errorTexts.commonTitle}</div>
      {dict.subtitle && <div style={{ color: "#7f1d1d", fontSize: 13, marginBottom: 8 }}>{dict.subtitle}</div>}
      <div style={{ color: "#7f1d1d", fontSize: 13, marginBottom: 12 }}>
        {errorTexts.traceIdPrefix}
        <code
          style={{ userSelect: "all", background: "#fee2e2", padding: "1px 4px", borderRadius: 4, marginLeft: 4 }}
          title="点击复制"
          onClick={async () => {
            try {
              if (traceId) {
                await navigator.clipboard.writeText(traceId);
                alert("trace_id 已复制");
              }
            } catch {
              /* no-op */
            }
          }}
        >
          {traceId || "-"}
        </code>
      </div>
      <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
        {onRetry && (
          <button
            onClick={onRetry}
            aria-label={errorTexts.actions.retry}
            style={{ background: "#ef4444", color: "#fff", borderRadius: 6, padding: "6px 12px" }}
          >
            {errorTexts.actions.retry}
          </button>
        )}
        <button
          onClick={report}
          aria-label={errorTexts.actions.report}
          style={{ background: "#fff", color: "#991b1b", border: "1px solid #fecaca", borderRadius: 6, padding: "6px 12px" }}
        >
          {errorTexts.actions.report}
        </button>

        {primaryAction && (
          <button
            onClick={primaryAction.onClick}
            aria-label={primaryAction.text}
            style={{ background: "#f3f4f6", color: "#111827", border: "1px solid #e5e7eb", borderRadius: 6, padding: "6px 12px" }}
          >
            {primaryAction.text}
          </button>
        )}
        {secondaryAction && (
          <button
            onClick={secondaryAction.onClick}
            aria-label={secondaryAction.text}
            style={{ background: "#fff", color: "#111827", border: "1px solid #e5e7eb", borderRadius: 6, padding: "6px 12px" }}
          >
            {secondaryAction.text}
          </button>
        )}
      </div>
      {extra && <div style={{ marginTop: 12 }}>{extra}</div>}
    </div>
  );
}