import React from "react";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, act } from "@testing-library/react";
import "@testing-library/jest-dom";
import { EmptyState, ErrorState, SkeletonList, SkeletonReader, SkeletonSessionHeader } from "./Placeholders";
import { errorTexts } from "../constants/ui";

// 提供全局替身，避免 jsdom 不支持引发的并发/cleanup 警告
vi.stubGlobal("alert", vi.fn());

describe("Placeholders 组件", () => {
  beforeEach(() => {
    vi.stubGlobal("navigator", { clipboard: { writeText: vi.fn().mockResolvedValue(undefined) } } as any);
    // 直接修改 window 对象，避免替换整个 window 原型
    // @ts-ignore
    window.location = { href: "", pathname: "/", search: "" } as any;
    // @ts-ignore
    window.scrollTo = () => {};
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.unstubAllGlobals();
    vi.restoreAllMocks();
  });

  it("EmptyState 渲染标题/副标题与 CTA", async () => {
    const onCta = vi.fn();
    render(<EmptyState title="标题" subtitle="副标题" ctaText="去创建第一条学习会话" onCta={onCta} />);
    expect(screen.getByText("标题")).toBeInTheDocument();
    expect(screen.getByText("副标题")).toBeInTheDocument();
    const btn = screen.getByRole("button", { name: "去创建第一条学习会话" });
    expect(btn).toBeInTheDocument();
  });
 
  it("ErrorState 显示统一错误文案与 trace_id，并可点击复制（unknown 类型）", async () => {
    render(<ErrorState page="reader" traceId="tid-abc" errorType="unknown" />);
    // unknown 类型标题来自字典
    expect(screen.getByText(errorTexts.types.unknown.title || errorTexts.commonTitle)).toBeInTheDocument();
    expect(screen.getByText(/问题追踪ID：/)).toBeInTheDocument();
    expect(screen.getByText("tid-abc")).toBeInTheDocument();
  });

  it("Skeleton 组件渲染不报错（Smoke Tests）", async () => {
    await act(async () => {
      render(<SkeletonList count={6} />);
      render(<SkeletonReader paragraphs={3} />);
      render(<SkeletonSessionHeader />);
    });
    expect(document.body).toBeInTheDocument();
  });

  it("ErrorState 显示 timeout 文案与 aria，无障碍属性存在", async () => {
    render(<ErrorState page="reader" traceId="tid-timeout" errorType="timeout" onRetry={() => {}} />);
    expect(screen.getByText(errorTexts.types.timeout.title)).toBeInTheDocument();
    expect(screen.getByText(errorTexts.types.timeout.subtitle)).toBeInTheDocument();
    // aria
    const region = screen.getByRole("alert");
    expect(region).toHaveAttribute("aria-live", "assertive");
    expect(region).toHaveAttribute("aria-label", errorTexts.aria.errorRegionLabel);
    // Retry CTA 存在
    expect(screen.getByRole("button", { name: errorTexts.actions.retry })).toBeInTheDocument();
  });

  it("SkeletonReader 在超过 longLoadingMs 后出现“仍在加载”提示 aria 文案", async () => {
    render(<SkeletonReader paragraphs={3} />);
    // 初始无长加载提示
    expect(screen.queryByTestId("loading-long-hint")).toBeNull();
    // 推进时钟至超过 longLoadingMs（在组件内部通过 longLoadingMs 控制）
    await act(async () => {
      vi.advanceTimersByTime(10_000 + 20);
    });
    expect(screen.getByTestId("loading-long-hint")).toHaveTextContent(errorTexts.aria.longLoading);
  });
});