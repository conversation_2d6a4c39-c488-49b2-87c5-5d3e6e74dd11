import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  getSessionById,
  type SessionDetailResponse,
  getSessionProgress,
  updateSessionProgress,
  sendMessage,
  classifyError,
} from "../api/client";
import {
  SkeletonReader,
  SkeletonSessionHeader,
  EmptyState,
  ErrorState,
} from "../components/Placeholders";
import { skeletonMinDurationMs, emptyTexts, longLoadingMs, streamingTexts, summaryTexts } from "../constants/ui";

/**
 * 可测试的调度适配层：在测试中可通过 vi.spyOn(schedule, 'timeout'/'raf'/...) 精确推进
 */
export const schedule = {
  timeout: (cb: (...args: any[]) => void, ms?: number) => setTimeout(cb, ms) as unknown as number,
  clearTimeout: (h: number) => clearTimeout(h as unknown as any),
  raf: (cb: FrameRequestCallback) => requestAnimationFrame(cb) as unknown as number,
  cancelAnimationFrame: (h: number) => cancelAnimationFrame(h as unknown as any),
  microtask: (cb: () => void = () => {}) => queueMicrotask(cb),
};

// ========== 工具函数与节流 ==========
function getClampedScrollY(raw: number): number {
  const doc = document.documentElement;
  const maxY = Math.max(0, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
  return Math.max(0, Math.min(Math.floor(raw), maxY));
}

function throttle<T extends (...args: any[]) => void>(fn: T, wait: number): T {
  // 经典节流：首次立即执行，后续以 wait 间隔执行；增加 trailing 触发
  let last = 0;
  let timer: number | undefined;
  return function (this: any, ...args: any[]) {
    const now = Date.now();
    const remaining = wait - (now - last);
    if (remaining <= 0) {
      last = now;
      if (timer) {
        window.clearTimeout(timer);
        timer = undefined;
      }
      fn.apply(this, args);
    } else if (!timer) {
      timer = window.setTimeout(() => {
        last = Date.now();
        timer = undefined;
        fn.apply(this, args);
      }, remaining);
    }
  } as T;
}

/**
 * Reader 视图
 * - 根据 :id 调用 GET /api/sessions/{id}
 * - 渲染标题与正文段落，包含加载/错误态
 * - 进度后端持久化：GET/PUT /api/sessions/{id}/progress
 */
export default function Reader() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [traceId, setTraceId] = React.useState<string | undefined>(() => {
    try {
      const url = new URL(window.location.href);
      const t = url.searchParams.get("trace_id") || undefined;
      return t || undefined;
    } catch {
      return undefined;
    }
  });

  // 状态机：loading | empty | error | ready
  type ViewState = "loading" | "empty" | "error" | "ready";
  const [state, setState] = React.useState<ViewState>("loading");
  const [errorMsg, setErrorMsg] = React.useState<string>("");
  const [data, setData] = React.useState<SessionDetailResponse | null>(null);
  const [errorType, setErrorType] = React.useState<"network" | "timeout" | "unauthorized" | "notfound" | "server" | "unknown">("unknown");
  const [isLongLoading, setIsLongLoading] = React.useState(false);

  // 统一受控 microtask 让步：React 渲染后确保测试可见
  const yieldMicrotask = React.useCallback(() => {
    try {
      if (typeof queueMicrotask === "function") queueMicrotask(() => {});
      else Promise.resolve().then(() => {});
    } catch {
      /* no-op */
    }
  }, []);

  // 仅用于控制恢复期间不触发保存
  const isRestoringRef = React.useRef(false);
  // 最近一次成功保存的进度（百分比），用于“≥3%提升才写”
  const lastSavedProgressRef = React.useRef<number>(-1);
  // 存储后端返回的 ETag（version），用于 If-Match
  const progressEtagRef = React.useRef<string | undefined>(undefined);

  // 本地进度缓存（用于与远端比较）
  const [localProgress, setLocalProgress] = React.useState<{ progress: number; updatedAt: string } | null>(null);

  // 冲突提示状态
  type ConflictInfo = {
    server: { offset: number; updatedAt?: string; version?: string; lastSource?: { deviceId?: string; userAgent?: string } };
    local: { offset: number; updatedAt: string };
    defaultChoice: "server" | "local" | "max";
  };
  const [conflict, setConflict] = React.useState<ConflictInfo | null>(null);
  const CONFLICT_TIME_WINDOW_MS = 5 * 60 * 1000; // 5 分钟
  const CONFLICT_DIFF_THRESHOLD = 100; // offset 差异阈值

  React.useEffect(() => {
    // eslint-disable-next-line no-console
    console.log(
      JSON.stringify(
        {
          event: "reader_view_open",
          trace_id: traceId,
          session_id: id || null,
          ts: new Date().toISOString(),
        },
        null,
        0
      )
    );
  }, [id, traceId]);

  // A. 绑定滚动监听：节流计算百分比并在阈值变化时 PUT 保存
  React.useEffect(() => {
    if (!id) return;

    const onScroll = throttle(async () => {
      if (isRestoringRef.current) return;
      try {
        const doc = document.documentElement;
        const top = window.scrollY || window.pageYOffset || 0;
        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
        const ratio = top / denom;
        const percent = Math.max(0, Math.min(100, Math.round(ratio * 100)));

        // 距上次成功保存提升 ≥3% 才进行写入
        const last = lastSavedProgressRef.current;
        if (last < 0 || percent - last >= 3) {
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          // 写入成功 -> 刷新本地 etag + 本地进度时间戳
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
          setLocalProgress({ progress: percent, updatedAt: new Date().toISOString() });
        }
      } catch (e: any) {
        if (e?.status === 409) {
          // 统一冲突处理：从服务端响应提取 server 信息，组装 conflict 状态
          const server = e?.responseJson?.server || {};
          const serverOffset = typeof server.offset === "number" ? server.offset : 0;
          const serverUpdatedAt = server.updatedAt || new Date().toISOString();
          const localOffset = lastSavedProgressRef.current >= 0 ? lastSavedProgressRef.current : 0;
          const localUpdatedAt = localProgress?.updatedAt || new Date().toISOString();

          // 默认策略：更新时间较新者优先
          const serverT = Date.parse(serverUpdatedAt) || 0;
          const localT = Date.parse(localUpdatedAt) || 0;
          let defaultChoice: "server" | "local" | "max" = serverT >= localT ? "server" : "local";

          setConflict({
            server: { offset: serverOffset, updatedAt: serverUpdatedAt, version: server.version, lastSource: server.lastSource },
            local: { offset: localOffset, updatedAt: localUpdatedAt },
            defaultChoice,
          });
        } else {
          // eslint-disable-next-line no-console
          console.warn("[reader] update progress failed (silent)", e);
        }
      }
    }, 333);

    window.addEventListener("scroll", onScroll, { passive: true });

    const onVisibilityChange = async () => {
      if (document.visibilityState === "hidden") {
        try {
          const doc = document.documentElement;
          const top = window.scrollY || window.pageYOffset || 0;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const percent = Math.max(0, Math.min(100, Math.round((top / denom) * 100)));
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
          setLocalProgress({ progress: percent, updatedAt: new Date().toISOString() });
        } catch (e) {
          // eslint-disable-next-line no-console
          console.warn("[reader] final save on hidden failed", e);
        }
      }
    };
    document.addEventListener("visibilitychange", onVisibilityChange);

    const onBeforeUnload = () => {
      // 最后一次保存采用 navigator.sendBeacon 不易用，这里保持同步 fetch 风险较大，故仅 best-effort 异步
      (async () => {
        try {
          const doc = document.documentElement;
          const top = window.scrollY || window.pageYOffset || 0;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const percent = Math.max(0, Math.min(100, Math.round((top / denom) * 100)));
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
          setLocalProgress({ progress: percent, updatedAt: new Date().toISOString() });
        } catch {
          /* no-op */
        }
      })();
    };
    window.addEventListener("beforeunload", onBeforeUnload);

    return () => {
      // 组件卸载/切换会话时，best-effort 保存一次
      (async () => {
        try {
          const doc = document.documentElement;
          const top = window.scrollY || window.pageYOffset || 0;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const percent = Math.max(0, Math.min(100, Math.round((top / denom) * 100)));
          const res = await updateSessionProgress(id, percent, { ifMatch: progressEtagRef.current });
          progressEtagRef.current = res._meta.etag || progressEtagRef.current;
          lastSavedProgressRef.current = percent;
        } catch {
          /* no-op */
        }
      })();
      window.removeEventListener("scroll", onScroll as any);
      window.removeEventListener("beforeunload", onBeforeUnload);
      document.removeEventListener("visibilitychange", onVisibilityChange);
    };
  }, [id]);

  // 标记首次加载流程是否已完成状态推进，避免并发/重复推进导致误判
  const didFinishRef = React.useRef(false);

  // B. 抽取首次加载流程为可复用的 load()，用于首载与重试
  const load = React.useCallback(async () => {
    if (!id) return;
    const startAt = Date.now();
    let cancelled = false;

    // 进入加载态并清理错误
    setState("loading");
    setErrorMsg("");
    setErrorType("unknown");
    setIsLongLoading(false);
    // 进入 loading 后，立刻让出一次微任务，帮助测试尽快看到 loading UI
    yieldMicrotask();

    // 10s 仍在加载提示
    const longTimer = schedule.timeout(() => {
      setIsLongLoading(true);
      // 让步到微任务，确保测试环境的 fake timers 能看到 DOM 更新
      schedule.microtask(() => {});
    }, longLoadingMs);
    // 为本次 load 创建总控制器，确保超时/取消能真正中止底层请求
    const controller = new AbortController();
 
    try {
      // 拉取详情（贯穿 signal 与总超时上限 10s）
      const detail = await getSessionById(id, { signal: controller.signal, totalTimeoutMs: 10_000 });
      if (cancelled) return;
      setData(detail);
      try {
        document.title = detail.title || "Reader";
      } catch {
        /* no-op */
      }

      // 优先使用详情中的 reading_position（1.9 契约）
      let progressPercent = 0;
      let usedDetailReadingPos = false;
      const detailReading = (detail as any).reading_position;
      if (typeof detailReading === "number" && isFinite(detailReading)) {
        progressPercent = Math.max(0, Math.min(100, Math.round(detailReading)));
        lastSavedProgressRef.current = progressPercent;
        // detail 不携带 ETag，需回退 GET /progress 获取最新 ETag 以保证后续 PUT 的并发安全
        try {
          const pr = await getSessionProgress(id, { signal: controller.signal, totalTimeoutMs: 10_000 });
          progressEtagRef.current = pr._meta?.etag || undefined;
          const anyPr: any = pr as any;
          const updatedAt = anyPr?.meta?.updatedAt || new Date().toISOString();
          setLocalProgress({ progress: progressPercent, updatedAt });
        } catch (e: any) {
          // eslint-disable-next-line no-console
          console.warn("[reader] get progress etag failed (detail reading used)", e);
          progressEtagRef.current = undefined;
          setLocalProgress({ progress: progressPercent, updatedAt: new Date().toISOString() });
        }
        usedDetailReadingPos = true;
      }

      // 若详情未提供 reading_position，则回退到独立进度接口
      if (!usedDetailReadingPos) {
        try {
          const pr = await getSessionProgress(id, { signal: controller.signal, totalTimeoutMs: 10_000 });
          progressPercent = Math.max(0, Math.min(100, Math.round(pr.progress ?? 0)));
          lastSavedProgressRef.current = progressPercent;
          progressEtagRef.current = pr._meta?.etag || undefined;
          const anyPr: any = pr as any;
          const updatedAt = anyPr?.meta?.updatedAt || new Date().toISOString();
          setLocalProgress({ progress: progressPercent, updatedAt });
        } catch (e: any) {
          // eslint-disable-next-line no-console
          console.warn("[reader] get progress failed, default 0", e);
          progressPercent = 0;
          lastSavedProgressRef.current = -1;
          progressEtagRef.current = undefined;
          setLocalProgress(null);
        }
      }

      // 恢复滚动（≤1s 窗口，rAF 连续尝试 + 超时兜底一次）
      if (progressPercent > 0) {
        const startAtRestore = Date.now();
        let attempts = 0;
        isRestoringRef.current = true;

        const doScrollToPercent = () => {
          const doc = document.documentElement;
          const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
          const target = getClampedScrollY(Math.round((progressPercent / 100) * denom));
          window.scrollTo(0, target);
        };

        const tryRestore = () => {
          attempts += 1;
          if (attempts === 1) {
            // eslint-disable-next-line no-console
            console.debug("[reader] restore progress", { sessionId: id, savedPercent: progressPercent });
          }
          doScrollToPercent();

          if (Date.now() - startAtRestore < 1000 && attempts < 6) {
            // 限制 rAF 链条为有限步数，避免在 fake timers 下无法推进
            schedule.raf(tryRestore as any);
          } else {
            schedule.timeout(() => {
              doScrollToPercent();
              isRestoringRef.current = false;
              // 在滚动恢复完成后，让出一次微任务，配合 RTL 观察渲染
              schedule.microtask(() => {});
            }, 0);
          }
        };

        schedule.raf(tryRestore as any);
      } else {
        window.scrollTo({ top: 0 });
      }

      // 成功路径：直接基于 detail 判定下一状态，保证骨架最少时长
      const paragraphs = detail.content?.paragraphs || [];
      const nextState: ViewState = paragraphs.length === 0 ? "empty" : "ready";
      const remain = Math.max(0, skeletonMinDurationMs - (Date.now() - startAt));
      schedule.timeout(() => {
        if (!cancelled) setState(nextState);
        // 结尾追加一次微任务让步，便于测试观察
        schedule.microtask(() => {});
      }, remain);
    } catch (e: any) {
      if (!cancelled) {
        const tid =
          e?.responseJson?.error?.trace_id ||
          e?.traceId ||
          undefined;
        setTraceId((prev) => prev || tid);
        setErrorMsg(e?.message || "加载失败，请稍后重试");

        // 使用统一 classifyError 结果映射 UI 类型，避免与 API 层逻辑偏差
        try {
          const ce = classifyError(e);
          const map = {
            network: "network",
            timeout: "timeout",
            unauthorized: "unauthorized",
            notfound: "notfound",
            server: "server",
            "server-retryable": "server" as const,
            unknown: "unknown",
          } as const;
          setErrorType(map[ce.type] as typeof errorType);
        } catch {
          // 回退：保持原有兜底路径
          let t: typeof errorType = "unknown";
          const status = (e as any)?.status;
          if (status === 401 || status === 403) t = "unauthorized";
          else if (status === 404) t = "notfound";
          else if (status >= 500) t = "server";
          else if ((e as any)?.name === "AbortError") t = "timeout";
          else if (e instanceof TypeError && ((e as any).message?.includes("fetch") || !("status" in (e as any)))) t = "network";
          setErrorType(t);
        }

        setData(null);
        const remain = Math.max(0, skeletonMinDurationMs - (Date.now() - startAt));
        schedule.timeout(() => {
          if (!cancelled) setState("error");
          schedule.microtask(() => {});
        }, remain);
      }
    }

    return () => {
      cancelled = true;
      try {
        window.clearTimeout(longTimer);
      } catch {
        /* no-op */
      }
      try {
        controller.abort();
      } catch {
        /* no-op */
      }
    };
  }, [id]);

  // B. 首次加载：在 id 变化时调用 load()
  React.useEffect(() => {
    if (!id) return;
    // 直接显式触发加载，避免依赖 setData(no-op) 的间接触发
    void load();
  }, [id, load]);

  // ========== 会话对话：流式发送与渲染（MVP） ==========
  const [input, setInput] = React.useState("");
  const [isSending, setIsSending] = React.useState(false);
  const [isLongSending, setIsLongSending] = React.useState(false);
  const [assistantText, setAssistantText] = React.useState("");
  const [summaryUpdated, setSummaryUpdated] = React.useState<string | null>(null);
  const sendControllerRef = React.useRef<AbortController | null>(null);
  // 发送失败/重试控制与信息提示（唯一定义）
  const [sendError, setSendError] = React.useState<string | null>(null);
  const [sendAttempt, setSendAttempt] = React.useState(0);
  const [sendAutoRetried, setSendAutoRetried] = React.useState(false);

  const handleStartLearning = () => {
    // 入口保留，功能由下方对话输入区承担
  };

  const cancelSending = () => {
    try {
      sendControllerRef.current?.abort();
    } catch {
      /* no-op */
    }
  };

  // 统一一次发送动作（包含一次自动重试）
  const doSend = React.useCallback(async () => {
    if (!id || !input.trim()) return;
    setIsSending(true);
    setAssistantText("");
    setSummaryUpdated(null);
    setSendError(null);
    const c = new AbortController();
    sendControllerRef.current = c;

    // 启动发送阶段10s长加载提示计时器
    const longSendTimer = schedule.timeout(() => {
      setIsLongSending(true);
      schedule.microtask(() => {});
    }, longLoadingMs);

    const tryOnce = async () => {
      const res = await sendMessage(id, input.trim(), {
        signal: c.signal,
        totalTimeoutMs: 10_000,
        onDelta: (d) => setAssistantText((prev) => prev + d),
      });
      if (res.summaryText) {
        setSummaryUpdated(summaryTexts.updatedHint);
        schedule.timeout(() => {
          setSummaryUpdated(null);
          schedule.microtask(() => {});
        }, 2000);
        setData((prev) => {
          if (!prev) return prev;
          const prevSummary: any = (prev as any).summary_latest;
          const nextVersion = prevSummary && typeof prevSummary.version === "number" ? prevSummary.version + 1 : 1;
          return {
            ...prev,
            summary_latest: prevSummary
              ? { ...(prevSummary as any), text: res.summaryText! }
              : {
                  id: "local",
                  session_id: prev.id,
                  version: nextVersion,
                  text: res.summaryText!,
                  created_at: new Date().toISOString(),
                },
          } as any;
        });
      }
    };

    try {
      await tryOnce();
      setSendAutoRetried(false);
    } catch (e1: any) {
      const ce = classifyError(e1);
      const retryable = ce.type === "network" || ce.type === "timeout" || ce.type === "server-retryable";
      if (retryable && !sendAutoRetried) {
        setSendAutoRetried(true);
        await new Promise((r) => schedule.timeout(r, 2000));
        try {
          await tryOnce();
          setSendError(null);
        } catch (e2: any) {
          setSendError(e2?.message || "发送失败，请重试");
        }
      } else {
        setSendError(e1?.message || "发送失败，请重试");
      }
    } finally {
      setIsSending(false);
      setIsLongSending(false);
      try { schedule.clearTimeout(longSendTimer as any); } catch {}
      sendControllerRef.current = null;
      schedule.microtask(() => {});
    }
    // 结束
  // 仅当 id 或 sendAttempt 改变时才重新绑定依赖，其余从闭包读取当前 input
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, sendAttempt]);

  const onSend = async () => {
    if (!id || !input.trim() || isSending) return;
    // 提升尝试计数，驱动 doSend 的依赖变化
    setSendAttempt((n) => n + 1);
    await doSend();
    // 根据测试期望，发送流程完成后清空输入
    setInput("");
    // 清空输入后让步一次，便于 RTL 立即观测到输入/按钮状态变化
    schedule.microtask(() => {});
  };

  const reload = () => {
    // 直接显式发起重新加载；新建控制器由 load 内部完成
    void load();
  };

  // 在视图渲染前让出一次微任务，确保上面首载状态切换在 fake timers 环境下完成一次 commit
  React.useEffect(() => {
    schedule.microtask(() => {});
  }, []);

  return (
    <div style={{ padding: 24 }}>
      <h1>阅读视图（Reader）</h1>

      {state === "loading" && (
        <>
          <SkeletonSessionHeader />
          <SkeletonReader />
          <div aria-hidden="true" role="status" style={{ position: "absolute", width: 0, height: 0, overflow: "hidden" }}>
            {streamingTexts.loading}
          </div>
          {isLongLoading && (
            <div
              data-testid="loading-long-hint"
              role="status"
              aria-live="polite"
              style={{ marginTop: 8, color: "#6b7280", fontSize: 12 }}
            >
              仍在加载，请稍候
            </div>
          )}
        </>
      )}

      {state === "error" && (
        <ErrorState
          page="reader"
          traceId={traceId}
          onRetry={reload}
          onReport={undefined}
          errorType={errorType}
          extra={
            <button onClick={() => navigate("/")} aria-label="返回 Library" style={{ marginTop: 8 }}>
              返回 Library
            </button>
          }
        />
      )}

      {state !== "loading" && state !== "error" && data && (
        <>
          <div style={{ marginBottom: 16 }}>
            <div style={{ color: "#667085", fontSize: 12 }}>会话ID：{data.id}</div>
            <h2 style={{ margin: "4px 0 0 0" }}>{data.title}</h2>
          </div>

          {/* 对话输入区（左栏） */}
          <div style={{ display: "flex", gap: 8, alignItems: "center", marginBottom: 16 }}>
            <input
              aria-label="输入你的问题"
              placeholder={isSending ? streamingTexts.loadingPlaceholder : "输入你的问题"}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              disabled={isSending}
              style={{ flex: 1, padding: "8px 10px", border: "1px solid #e5e7eb", borderRadius: 6 }}
            />
            <button onClick={onSend} disabled={isSending || !input.trim()}>
              {isSending ? streamingTexts.sendButton.loading : streamingTexts.sendButton.idle}
            </button>
            {isSending && (
              <button onClick={cancelSending} aria-label="取消生成">
                {streamingTexts.sendButton.cancel}
              </button>
            )}
            <button onClick={() => navigate("/")}>返回 Library</button>
          </div>

          {/* 摘要区块：当存在 summary_latest 时展示文本，否则展示空态占位 */}
          <div style={{ marginBottom: 20 }}>
            <h3>摘要</h3>
            {summaryUpdated && (
              <div role="status" aria-live="polite" style={{ fontSize: 12, color: "#059669", marginBottom: 6 }}>
                {summaryUpdated}
              </div>
            )}
            {data.summary_latest && (data.summary_latest as any).text ? (
              <div
                aria-label="summary-latest"
                style={{
                  border: "1px solid #e5e7eb",
                  background: "#f9fafb",
                  padding: 12,
                  borderRadius: 8,
                  whiteSpace: "pre-wrap",
                  lineHeight: 1.6,
                }}
              >
                {(data.summary_latest as any).text}
              </div>
            ) : (
              <EmptyState
                title={emptyTexts.summary.title}
                subtitle={emptyTexts.summary.subtitle}
              />
            )}
          </div>

          <div>
            <h3>原文</h3>
            {state === "empty" ? (
              <EmptyState
                title={emptyTexts.reader.title}
                subtitle={emptyTexts.reader.subtitle}
                ctaText={emptyTexts.library.cta}
                onCta={() => navigate("/")}
              />
            ) : (
              <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
                {data.content.paragraphs.map((p) => (
                  <p key={p.index} style={{ lineHeight: 1.7, margin: 0 }}>
                    {p.text}
                  </p>
                ))}
              </div>
            )}

            {/* 助手回答（流式渲染） */}
            {assistantText && (
              <div
                aria-live="polite"
                aria-label={streamingTexts.a11y.assistantLiveRegion}
                style={{
                  border: "1px dashed #e5e7eb",
                  background: "#ffffff",
                  padding: 12,
                  borderRadius: 8,
                  whiteSpace: "pre-wrap",
                  lineHeight: 1.6,
                  marginTop: 16,
                }}
              >
                {assistantText}
              </div>
            )}
            {/* 发送错误与手动重试入口 */}
            {sendError && (
              <div role="alert" aria-live="polite" style={{ color: "#b91c1c", marginTop: 8 }}>
                {sendError}
                <button
                  style={{ marginLeft: 8 }}
                  onClick={async () => {
                    setSendAttempt((n) => n + 1);
                    await doSend();
                  }}
                  aria-label="重试发送"
                >
                  重试
                </button>
              </div>
            )}

            {/* 冲突提示（轻量浮层，不阻断阅读） */}
            {conflict && (
              <div
                role="dialog"
                aria-live="polite"
                style={{
                  position: "fixed",
                  right: 16,
                  bottom: 16,
                  maxWidth: 420,
                  background: "#ffffff",
                  border: "1px solid #e5e7eb",
                  boxShadow:
                    "0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05)",
                  borderRadius: 8,
                  padding: 12,
                  zIndex: 1000,
                }}
              >
                <div style={{ fontWeight: 600, marginBottom: 6 }}>检测到进度冲突</div>
                <div style={{ fontSize: 12, color: "#667085", marginBottom: 8 }}>
                  服务端 {conflict.server.updatedAt?.replace("T", " ").replace("Z", "")}（设备 {conflict.server.lastSource?.deviceId || "未知"}）
                  vs 本地 {conflict.local.updatedAt.replace("T", " ").replace("Z", "")}
                </div>
                <div style={{ fontSize: 12, color: "#475467", marginBottom: 8 }}>
                  选择要采用的进度：
                </div>
                <div style={{ display: "flex", gap: 8, flexWrap: "wrap" }}>
                  <button
                    style={{
                      background: conflict.defaultChoice === "server" ? "#0ea5e9" : "#f3f4f6",
                      color: conflict.defaultChoice === "server" ? "#fff" : "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={async () => {
                      if (!id) return;
                      try {
                        const target = conflict.server.offset;
                        const res = await updateSessionProgress(id, target, {
                          ifMatch: conflict.server.version || progressEtagRef.current,
                        });
                        progressEtagRef.current = res._meta.etag || progressEtagRef.current;
                        lastSavedProgressRef.current = target;
                        setLocalProgress({ progress: target, updatedAt: new Date().toISOString() });
                        setConflict(null);
                        // 平滑滚到目标
                        const doc = document.documentElement;
                        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
                        const y = Math.round((target / 100) * denom);
                        window.scrollTo({ top: y, behavior: "smooth" });
                      } catch (e) {
                        // eslint-disable-next-line no-console
                        console.warn("[reader] apply server failed", e);
                      }
                    }}
                  >
                    使用远端（{conflict.server.offset}%）
                  </button>

                  <button
                    style={{
                      background: conflict.defaultChoice === "local" ? "#0ea5e9" : "#f3f4f6",
                      color: conflict.defaultChoice === "local" ? "#fff" : "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={async () => {
                      if (!id) return;
                      try {
                        // 采用本地：先 GET 刷新最新 ETag，再 PUT
                        const pr = await getSessionProgress(id);
                        progressEtagRef.current = pr._meta?.etag || progressEtagRef.current;
                        const target = conflict.local.offset;
                        const res = await updateSessionProgress(id, target, { ifMatch: progressEtagRef.current });
                        progressEtagRef.current = res._meta.etag || progressEtagRef.current;
                        lastSavedProgressRef.current = target;
                        setLocalProgress({ progress: target, updatedAt: new Date().toISOString() });
                        setConflict(null);
                        const doc = document.documentElement;
                        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
                        const y = Math.round((target / 100) * denom);
                        window.scrollTo({ top: y, behavior: "smooth" });
                      } catch (e) {
                        // eslint-disable-next-line no-console
                        console.warn("[reader] apply local failed", e);
                      }
                    }}
                  >
                    保留本地（{conflict.local.offset}%）
                  </button>

                  <button
                    style={{
                      background: conflict.defaultChoice === "max" ? "#0ea5e9" : "#f3f4f6",
                      color: conflict.defaultChoice === "max" ? "#fff" : "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={async () => {
                      if (!id) return;
                      try {
                        const target = Math.max(conflict.local.offset, conflict.server.offset);
                        const res = await updateSessionProgress(id, target, {
                          ifMatch: conflict.server.version || progressEtagRef.current,
                        });
                        progressEtagRef.current = res._meta.etag || progressEtagRef.current;
                        lastSavedProgressRef.current = target;
                        setLocalProgress({ progress: target, updatedAt: new Date().toISOString() });
                        setConflict(null);
                        const doc = document.documentElement;
                        const denom = Math.max(1, (doc?.scrollHeight || 0) - (window.innerHeight || 0));
                        const y = Math.round((target / 100) * denom);
                        window.scrollTo({ top: y, behavior: "smooth" });
                      } catch (e) {
                        // eslint-disable-next-line no-console
                        console.warn("[reader] apply max failed", e);
                      }
                    }}
                  >
                    合并（取最大 {Math.max(conflict.local.offset, conflict.server.offset)}%）
                  </button>

                  <button
                    style={{
                      background: "#ffffff",
                      color: "#111827",
                      border: "1px solid #e5e7eb",
                      borderRadius: 6,
                      padding: "6px 10px",
                    }}
                    onClick={() => setConflict(null)}
                  >
                    稍后
                  </button>
                </div>
              </div>
            )}
          </div>
        </>
      )}

      <div style={{ marginTop: 24 }}>
        <h3>诊断信息</h3>
        <pre style={{ background: "#f6f8fa", padding: 12, borderRadius: 6, overflowX: "auto" }}>
{JSON.stringify(
  {
    sessionId: id,
    trace_id: traceId,
    state,
    etag: progressEtagRef.current,
    localProgress,
    conflictActive: !!conflict,
    sending: isSending,
    assistantTextLen: assistantText.length,
    summaryUpdated,
  },
  null,
  2
)}
        </pre>
      </div>
    </div>
  );
}