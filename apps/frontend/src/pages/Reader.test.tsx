import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { render, screen, act, waitFor } from "@testing-library/react";
import { MemoryRouter } from "react-router-dom";
import Reader from "./Reader";
import * as api from "../api/client";
import { summaryTexts } from "../constants/ui";

/**
 * 关键修复点：
 * - 使用全局推进工具 __advanceAndFlush__ 统一处理定时器和 React 更新
 * - 移除对 window 的直接引用，避免环境问题
 * - 优化时序控制，确保测试稳定性
 */

declare global {
  var __advanceAndFlush__: (ms?: number) => Promise<void>;
}

describe("Reader 页面 - 重试/摘要提示/长加载提示", () => {
  const TEST_TIMEOUT = 30_000;

  // 统一推进工具
  async function advance(ms = 0) {
    await globalThis.__advanceAndFlush__(ms);
  }

  async function advanceUntil(targetMs: number, step = 100) {
    let elapsed = 0;
    while (elapsed < targetMs) {
      const nextStep = Math.min(step, targetMs - elapsed);
      await advance(nextStep);
      elapsed += nextStep;
    }
  }

  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(new Date("2025-01-01T00:00:00.000Z"));

    // stub 基础数据，避免 Reader 首载阶段卡住
    vi.spyOn(api, "getSessionById").mockResolvedValue({
      id: "s1",
      title: "T",
      content: { paragraphs: [{ index: 0, text: "p0" }] },
    } as any);
    vi.spyOn(api, "getSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 0,
      _meta: { etag: "v" },
      meta: { updatedAt: "2025-01-01T00:00:00.000Z" },
    } as any);

    // 抑制副作用，避免干扰测试
    vi.spyOn(api, "updateSessionProgress").mockResolvedValue({
      session_id: "s1",
      progress: 0,
      _meta: { etag: "v" },
    } as any);
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  it(
    "长加载时显示 aria-live 提示",
    async () => {
      // 模拟长时间加载：getSessionById 延迟返回
      const sessionPromise = new Promise<any>(() => {}); // 永不resolve
      vi.spyOn(api, "getSessionById").mockReturnValue(sessionPromise);

      render(
        <MemoryRouter initialEntries={["/reader/test-session"]}>  
          <Reader />
        </MemoryRouter>
      );

      // 等待组件开始加载流程
      await advance(100);

      // 分段推进到 10s+ 以触发长加载提示
      await advanceUntil(10_200, 200);

      // 等待长加载提示出现
      await waitFor(
        () => {
          // 查找包含长加载相关文字的 aria-live 元素
          const elements = screen.queryAllByRole("status");
          const longLoadingElement = elements.find((el) => 
            /仍在加载|请稍候|加载较久/.test(el.textContent || "")
          );
          expect(longLoadingElement).toBeInTheDocument();
        },
        { timeout: 5000 }
      );
    },
    TEST_TIMEOUT
  );

  it(
    "错误后自动重试失败再显示错误，手动重试后成功",
    async () => {
      const err = Object.assign(new Error("network"), { name: "TypeError" });
      const sendSpy = vi
        .spyOn(api, "sendMessage")
        .mockRejectedValueOnce(err)
        .mockRejectedValueOnce(err)
        .mockResolvedValueOnce({ fullText: "ok", _traceId: "tid-ok" } as any);

      render(
        <MemoryRouter initialEntries={["/reader/test-session"]}>
          <Reader />
        </MemoryRouter>
      );

      // 等待组件加载完成，进入 ready 状态
      await advance(200);
      
      await waitFor(() => {
        expect(screen.queryByRole("textbox")).toBeInTheDocument();
      }, { timeout: 5000 });

      // 填写输入框并发送消息，触发第一次失败
      const input = screen.getByRole("textbox", { name: /输入你的问题/i });
      await act(async () => {
        (input as HTMLInputElement).value = "test message";
        input.dispatchEvent(new Event("input", { bubbles: true }));
      });

      await advance(50);

      const sendBtn = screen.getByRole("button", { name: /发送|生成中/i });
      await act(async () => {
        sendBtn.click();
      });

      // 等待第一次失败和自动重试窗口(2s)
      await advanceUntil(2_200, 100);

      // 应该出现重试按钮
      await waitFor(
        () => {
          const btn = screen.queryByRole("button", { name: /重试/i });
          expect(btn).toBeInTheDocument();
        },
        { timeout: 5000 }
      );
      
      const retryBtn = screen.getByRole("button", { name: /重试/i });
      await act(async () => {
        retryBtn.click();
      });

      await advance(100);

      // 两次自动失败 + 一次手动成功
      expect(sendSpy).toHaveBeenCalledTimes(3);
    },
    TEST_TIMEOUT
  );

  it(
    "收到摘要更新时，出现并短暂显示提示",
    async () => {
      const sendSpy = vi.spyOn(api, "sendMessage").mockResolvedValue({
        fullText: "answer",
        summaryText: "summary-updated",
        _traceId: "tid-sum",
      } as any);

      render(
        <MemoryRouter initialEntries={["/reader/test-session"]}>
          <Reader />
        </MemoryRouter>
      );

      // 等待组件加载完成，进入 ready 状态
      await advance(200);
      
      await waitFor(() => {
        expect(screen.queryByRole("textbox")).toBeInTheDocument();
      }, { timeout: 5000 });

      const input = screen.getByRole("textbox", { name: /输入你的问题/i });
      await act(async () => {
        (input as HTMLInputElement).value = "hi";
        input.dispatchEvent(new Event("input", { bubbles: true }));
      });

      await advance(50);

      const sendBtn = screen.getByRole("button", { name: /发送|生成中/i });
      await act(async () => {
        sendBtn.click();
      });

      // 等待请求完成，摘要提示应该出现
      await advance(100);

      await waitFor(
        () => {
          const element = screen.queryByText(new RegExp(summaryTexts.updated));
          expect(element).toBeInTheDocument();
        },
        { timeout: 5000 }
      );

      // 推进 2s 以触发隐藏
      await advanceUntil(2_200, 100);

      await waitFor(
        () => {
          expect(screen.queryByText(new RegExp(summaryTexts.updated))).toBeNull();
        },
        { timeout: 3000 }
      );

      sendSpy.mockRestore();
    },
    TEST_TIMEOUT
  );
});