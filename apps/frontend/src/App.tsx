import React, { useEffect, useMemo, useRef, useState } from "react";
import { Routes, Route, useNavigate } from "react-router-dom";
import Reader from "./pages/Reader";
import { createSession, getSessions, type SessionListItem } from "./api/client";
import { SkeletonList, EmptyState, ErrorState } from "./components/Placeholders";
import { emptyTexts, errorTexts, skeletonMinDurationMs } from "./constants/ui";

/**
 * Home 作为 Library 列表页 + 最小创建入口
 * - 接入统一：加载骨架 / 空历史 / 错误占位（含 trace_id 与重试/反馈）
 */
function Home() {
  const [text, setText] = useState("这是一段测试文本，用于创建学习会话。");
  const [loadingCreate, setLoadingCreate] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string>("");
  const [traceIdForLastReq, setTraceIdForLastReq] = useState<string>("");

  const [sessions, setSessions] = useState<SessionListItem[] | null>(null);
  const [state, setState] = useState<"loading" | "empty" | "error" | "ready">("loading");
  const [listTraceId, setListTraceId] = useState<string | undefined>(undefined);
  const minSkeletonTimerRef = useRef<number | null>(null);

  const navigate = useNavigate();

  // 懒生成 trace_id，贯穿一次创建操作
  const createTraceId = useMemo(() => crypto.randomUUID(), []);

  const clearMinTimer = () => {
    if (minSkeletonTimerRef.current) {
      window.clearTimeout(minSkeletonTimerRef.current);
      minSkeletonTimerRef.current = null;
    }
  };

  const loadList = async () => {
    clearMinTimer();
    setState("loading");
    const start = Date.now();
    try {
      const resp = await getSessions(50, 0);
      const showAtLeast = Math.max(0, skeletonMinDurationMs - (Date.now() - start));
      minSkeletonTimerRef.current = window.setTimeout(() => {
        setSessions(resp.items || []);
        setState((resp.items || []).length === 0 ? "empty" : "ready");
      }, showAtLeast);
    } catch (e: any) {
      const trace = e?.traceId || e?.responseJson?.error?.trace_id;
      setListTraceId(trace);
      // eslint-disable-next-line no-console
      console.info("[ListError]", {
        page: "library",
        trace_id: trace,
        path: location.pathname + location.search,
        retryable: true,
      });
      const showAtLeast = Math.max(0, skeletonMinDurationMs - (Date.now() - start));
      minSkeletonTimerRef.current = window.setTimeout(() => {
        setSessions([]);
        setState("error");
      }, showAtLeast);
    }
  };

  useEffect(() => {
    let cancelled = false;
    (async () => {
      await loadList();
      if (cancelled) return;
    })();
    return () => {
      cancelled = true;
      clearMinTimer();
    };
  }, []);

  const onCreate = async () => {
    if (!text.trim()) {
      setErrorMsg("请输入要学习的文本内容。");
      return;
    }
    setLoadingCreate(true);
    setErrorMsg("");
    try {
      const resp = await createSession(text, createTraceId);
      // eslint-disable-next-line no-console
      console.log(
        JSON.stringify(
          { event: "create_session_success", trace_id: createTraceId, session_id: resp.id, ts: new Date().toISOString() },
          null,
          0
        )
      );
      setTraceIdForLastReq(createTraceId);
      navigate(`/reader/${resp.id}?trace_id=${encodeURIComponent(createTraceId)}`);
    } catch (e: any) {
      const uiMsg =
        e?.responseJson?.error?.message ||
        e?.message ||
        "创建会话失败，请稍后重试。";
      const trace = e?.traceId || e?.responseJson?.error?.trace_id || createTraceId;
      setTraceIdForLastReq(trace);
      setErrorMsg(`${uiMsg}（trace_id: ${trace}）`);
      // eslint-disable-next-line no-console
      console.error("create_session_failed", { trace_id: trace, raw: e });
    } finally {
      setLoadingCreate(false);
    }
  };

  const copyTraceId = async () => {
    if (!traceIdForLastReq) return;
    try {
      await navigator.clipboard.writeText(traceIdForLastReq);
      alert("trace_id 已复制");
    } catch {
      alert("复制失败，请手动复制");
    }
  };

  // 简易相对时间函数，避免额外依赖
  function timeAgo(iso: string): string {
    const now = Date.now();
    const t = new Date(iso).getTime();
    const diff = Math.max(0, Math.floor((now - t) / 1000));
    if (diff < 60) return `${diff} 秒前`;
    const m = Math.floor(diff / 60);
    if (m < 60) return `${m} 分钟前`;
    const h = Math.floor(m / 60);
    if (h < 24) return `${h} 小时前`;
    const d = Math.floor(h / 24);
    if (d < 7) return `${d} 天前`;
    const w = Math.floor(d / 7);
    if (w < 4) return `${w} 周前`;
    const mo = Math.floor(d / 30);
    if (mo < 12) return `${mo} 个月前`;
    const y = Math.floor(d / 365);
    return `${y} 年前`;
  }

  return (
    <div style={{ padding: 24, fontFamily: "Inter, system-ui, -apple-system, Segoe UI, Roboto, 'Helvetica Neue', Arial" }}>
      <h1>Zhiread Library</h1>
      <p>会话列表（GET /api/sessions）</p>

      <div style={{ marginBottom: 24, padding: 12, border: "1px solid #eee", borderRadius: 6 }}>
        <h3 style={{ marginTop: 0 }}>快速创建一个会话</h3>
        <textarea
          value={text}
          onChange={(e) => setText(e.target.value)}
          rows={4}
          style={{ width: "100%", marginBottom: 12 }}
          placeholder="粘贴你的文本..."
          disabled={loadingCreate}
        />
        <div style={{ display: "flex", gap: 12, alignItems: "center" }}>
          <button onClick={onCreate} disabled={loadingCreate}>
            {loadingCreate ? "创建中…" : "创建会话"}
          </button>
          {errorMsg && (
            <>
              <span style={{ color: "#b42318" }}>{errorMsg}</span>
              {traceIdForLastReq && (
                <button onClick={copyTraceId} title="复制 trace_id">复制 trace_id</button>
              )}
            </>
          )}
        </div>
      </div>

      <div>
        <h3 style={{ marginTop: 0 }}>我的会话</h3>

        {state === "loading" ? (
          <SkeletonList />
        ) : state === "error" ? (
          <ErrorState
            page="library"
            traceId={listTraceId}
            onRetry={loadList}
            onReport={() => {
              // 交由组件构造 mailto，或在这里自定义
              window.location.href = `mailto:<EMAIL>?subject=${encodeURIComponent("Zhiread 列表报错")}&body=${encodeURIComponent(
                `trace_id: ${listTraceId || "-"}\nURL: ${location.href}`
              )}`;
            }}
          />
        ) : state === "empty" ? (
          <EmptyState
            title={emptyTexts.library.title}
            subtitle={emptyTexts.library.subtitle}
            ctaText={emptyTexts.library.cta}
            onCta={() => {
              window.scrollTo({ top: 0, behavior: "smooth" });
            }}
          />
        ) : (
          sessions && sessions.length > 0 && (
            <ul style={{ listStyle: "none", padding: 0, margin: 0, display: "flex", flexDirection: "column", gap: 8 }}>
              {sessions.map((s) => (
                <li key={s.id} style={{ border: "1px solid #eee", borderRadius: 6, padding: 12, cursor: "pointer" }}>
                  <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                    <div>
                      <div style={{ fontWeight: 600 }}>{s.title}</div>
                      <div style={{ color: "#667085", fontSize: 12 }}>
                        创建于：{timeAgo(s.created_at)}（{new Date(s.created_at).toLocaleString()}）
                      </div>
                    </div>
                    <button onClick={() => navigate(`/reader/${s.id}`)}>阅读</button>
                  </div>
                </li>
              ))}
            </ul>
          )
        )}
      </div>
    </div>
  );
}

export default function App() {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/reader/:id" element={<Reader />} />
    </Routes>
  );
}