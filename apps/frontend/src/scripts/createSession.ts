import { createSession } from "../api/client";

async function main() {
  const args = new URLSearchParams(location.search);
  const text = args.get("text") || "这是一段测试文本，用于创建学习会话。";
  const traceId = args.get("trace_id") || undefined;

  try {
    const resp = await createSession(text, traceId || undefined);
    console.log("Create session success:", resp);
    const pre = document.createElement("pre");
    pre.textContent = JSON.stringify(resp, null, 2);
    document.body.appendChild(pre);
  } catch (e: any) {
    console.error("Create session failed:", e);
    const pre = document.createElement("pre");
    pre.textContent = JSON.stringify(
      e?.responseJson ?? { error: e?.message || "unknown" },
      null,
      2
    );
    document.body.appendChild(pre);
  }
}

main().catch((e) => console.error(e));