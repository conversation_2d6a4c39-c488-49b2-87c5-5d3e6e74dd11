const API_BASE = (import.meta as any).env?.VITE_API_BASE || "http://localhost:8000/api";

/**
 * 消息发送统一类型（MVP）
 * - 支持两种后端返回模式：
 *   1) 流式 text/event-stream：逐块产出 { delta: string }，结束时可包含 { summary?: { text: string } }
 *   2) 最终 JSON：一次性返回 { content: string, summary?: { text: string } }
 * - 前端统一通过回调/Promise 消费，保证两种模式下行为一致
 */
export type SendMessageChunk =
  | { type: "delta"; text: string }
  | { type: "done"; summaryText?: string; traceId?: string };

export interface SendMessageOptions {
  signal?: AbortSignal;
  totalTimeoutMs?: number; // 总超时上限，默认 10s（沿用 1.11）
  // 回调：接收流式 delta；在最终 JSON 模式下会在完成时以一次性 delta 形式回放（保证 UI 一致）
  onDelta?: (delta: string) => void;
}

export interface CreateSessionResponse {
  id: string;
  title: string;
  created_at: string; // UTC ISO8601 with Z
}

export interface SessionListItem {
  id: string;
  title: string;
  created_at: string;
}

export interface SessionListResponse {
  items: SessionListItem[];
  total: number;
}

export interface SessionParagraph {
  index: number;
  text: string;
}

export interface SessionContent {
  language: string;
  source: string;
  paragraphs: SessionParagraph[];
}

// 1.9 扩展：对话消息与摘要（与后端架构共享类型对齐）
export interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  created_at: string; // UTC ISO8601 with Z
}

export interface SummaryLatest {
  id: string;
  session_id: string;
  version: number;
  text: string;
  created_at: string; // UTC ISO8601 with Z
}

export interface SessionDetailResponse extends SessionListItem {
  content: SessionContent;
  messages?: Message[]; // 升序
  summary_latest?: SummaryLatest | null;
  reading_position?: number; // 0-100 百分比
}

export interface SessionProgressResponse {
  session_id: string;
  progress: number; // 0-100
}

export interface ProgressMeta {
  etag?: string; // ETag header carrying version
  updatedAt?: string;
  lastSource?: { deviceId?: string; userAgent?: string };
}

export class ApiError extends Error {
  status: number;
  responseJson?: any;
  traceId?: string;
  constructor(msg: string, status: number, json?: any, traceId?: string) {
    super(msg);
    this.status = status;
    this.responseJson = json;
    this.traceId = traceId;
  }
}

// ---- Story 1.11: 统一错误归类 & 指数退避 + 可中止请求贯穿 ----
export type ClassifiedErrorType =
  | "network"
  | "timeout"
  | "unauthorized"
  | "notfound"
  | "server"
  | "server-retryable" // 新增：可重试 5xx（如 502/503/504）
  | "unknown";
export interface ClassifiedError {
  type: ClassifiedErrorType;
  messageKey: string; // 用于 UI 文案 key
  original?: any;
  retryAfterMs?: number; // 当可重试时的 Retry-After 建议（毫秒）
}

// 统一错误归类（增强：AbortError/DOMException('AbortError') 归为 timeout；TypeError 无 status 归为 network）
export function classifyError(error: any): ClassifiedError {
  // Abort 中止：DOMException('AbortError') 或 name === 'AbortError'
  const isAbortError =
    error?.name === "AbortError" ||
    (typeof DOMException !== "undefined" && error instanceof DOMException && error.name === "AbortError");
  if (isAbortError) {
    return { type: "timeout", messageKey: "error.timeout", original: error };
  }
  // 部分浏览器/环境网络失败为 TypeError 且无 status
  if (error instanceof TypeError && !("status" in (error as any))) {
    return { type: "network", messageKey: "error.network", original: error };
  }

  // ApiError（由 handleResponse 抛出）
  if (error instanceof ApiError) {
    const status = error.status;
    const raw = error.responseJson;
    // 解析 Retry-After（秒或日期）
    const retryAfterHeader =
      typeof raw === "object" && raw && typeof raw === "object" && "headers" in (raw as any)
        ? undefined
        : undefined; // 不从 body 读，实际 header 在 Response，这里不可得；退化为仅基于 status 分类
    let retryAfterMs: number | undefined = undefined;
    // 分类
    if (status === 401 || status === 403) {
      return { type: "unauthorized", messageKey: "error.unauthorized", original: error };
    }
    if (status === 404) {
      return { type: "notfound", messageKey: "error.notfound", original: error };
    }
    if (status === 502 || status === 503 || status === 504) {
      return { type: "server-retryable", messageKey: "error.server.retryable", original: error, retryAfterMs };
    }
    if (status >= 500) {
      return { type: "server", messageKey: "error.server", original: error };
    }
    // 其他 4xx
    return { type: "unknown", messageKey: "error.unknown", original: error };
  }

  // 兜底
  return { type: "unknown", messageKey: "error.unknown", original: error };
}

// 创建“总体超时控制”的包装器：在总时限触发时主动 abort，并向底层传入 signal
export async function fetchWithTimeout<T>(
  fn: (signal: AbortSignal) => Promise<T>,
  opts?: { totalTimeoutMs?: number; externalSignal?: AbortSignal }
): Promise<T> {
  const total = Math.max(1, opts?.totalTimeoutMs ?? 10_000);
  const controller = new AbortController();
  const timer = setTimeout(() => controller.abort(), total);

  // 若传入外部 signal，则建立中止联动（使用工具函数）
  if (opts?.externalSignal) {
    try {
      const { linkAbortSignals } = await import("./sse");
      const cleanup = linkAbortSignals(controller, opts.externalSignal);
      // 定时器清理时一并清理联动监听
      const orig = clearTimeout;
      // 不替换全局 clearTimeout，仅在 finally 里执行 cleanup
      (controller as any).__cleanupExternalLink = cleanup;
    } catch {
      // 回退到本地实现
      if (opts.externalSignal.aborted) {
        clearTimeout(timer);
        controller.abort();
      } else {
        const onAbort = () => controller.abort();
        opts.externalSignal.addEventListener("abort", onAbort, { once: true });
        (controller as any).__onAbort = onAbort;
        (controller as any).__ext = opts.externalSignal;
      }
    }
  }

  try {
    return await fn(controller.signal);
  } finally {
    clearTimeout(timer);
    // 清理外部联动监听
    try {
      const cleanup: undefined | (() => void) = (controller as any).__cleanupExternalLink;
      if (cleanup) cleanup();
    } catch {}
    try {
      const ext: AbortSignal | undefined = (controller as any).__ext;
      const onAbort: any = (controller as any).__onAbort;
      if (ext && onAbort) ext.removeEventListener("abort", onAbort);
    } catch {}
  }
}

// 指数退避重试（修改：向 fn 传入 signal；每次尝试都有自己的局部控制器，同时受外部/总超时影响）
export async function retryWithBackoff<T>(
  fn: (signal: AbortSignal) => Promise<T>,
  opts?: {
    max?: number;
    base?: number;
    classify?: (e: any) => ClassifiedError;
    totalTimeoutMs?: number;
    signal?: AbortSignal; // 外部传入的中止信号
    respectRetryAfter?: boolean; // 新增：遵循 Retry-After（默认 true）
    retryableStatuses?: number[]; // 新增：可重试状态码列表（默认 [502,503,504]）
  }
): Promise<T> {
  const max = Math.max(0, opts?.max ?? 3);
  const base = Math.max(1, opts?.base ?? 500);
  const classify = opts?.classify ?? classifyError;
  const totalTimeoutMs = Math.max(1, opts?.totalTimeoutMs ?? 10_000);
  const respectRetryAfter = opts?.respectRetryAfter ?? true;
  const retryableStatuses = opts?.retryableStatuses ?? [502, 503, 504];

  // 总体超时控制：超出总时限后终止后续尝试
  const totalController = new AbortController();
  const totalTimer = setTimeout(() => totalController.abort(), totalTimeoutMs);

  // 外部 signal 联动
  if (opts?.signal) {
    try {
      const { linkAbortSignals } = await import("./sse");
      const cleanup = linkAbortSignals(totalController, opts.signal);
      (totalController as any).__cleanupExternalLink = cleanup;
    } catch {
      if (opts.signal.aborted) {
        clearTimeout(totalTimer);
        totalController.abort();
      } else {
        const onAbort = () => totalController.abort();
        opts.signal.addEventListener("abort", onAbort, { once: true });
        (totalController as any).__onAbort = onAbort;
        (totalController as any).__ext = opts.signal;
      }
    }
  }

  let attempt = 0;
  let lastErr: any;

  try {
    while (attempt <= max) {
      // 每次尝试创建子控制器，并受总控制器约束
      const attemptController = new AbortController();
      const linkAbort = () => attemptController.abort();
      if (totalController.signal.aborted) {
        // 总体已中止
        throw new DOMException("The operation was aborted.", "AbortError");
      }
      totalController.signal.addEventListener("abort", linkAbort, { once: true });

      try {
        const res = await fn(attemptController.signal);
        return res;
      } catch (e: any) {
        lastErr = e;
        const ce = classify(e);

        // 决定是否重试：
        // 1) 网络错误/超时 -> 重试
        // 2) 可重试 Server 错误（502/503/504）-> 重试
        // 其他 -> 不重试
        const isRetryableServer =
          (e instanceof ApiError && retryableStatuses.includes(e.status)) ||
          ce.type === "server-retryable";

        const shouldRetry = ce.type === "network" || ce.type === "timeout" || isRetryableServer;

        if (!shouldRetry) {
          throw e;
        }

        if (attempt === max) break;

        // 等待时长：优先使用 Retry-After（若可用且开启），否则指数退避
        let delay = base * Math.pow(2, attempt); // 500, 1000, 2000...
        if (respectRetryAfter) {
          // 从 ApiError 原始响应副信息中很难拿到 header；此处仅支持 classify 提供的 retryAfterMs
          if (typeof ce.retryAfterMs === "number" && ce.retryAfterMs >= 0) {
            delay = ce.retryAfterMs;
          } else if (e instanceof ApiError) {
            // 尝试从 responseJson 推断（后端可选择回传 meta.retry_after_ms）
            const ra =
              (e.responseJson && (e.responseJson.retry_after_ms ?? e.responseJson?.meta?.retry_after_ms)) || undefined;
            if (typeof ra === "number" && ra >= 0) {
              delay = ra;
            }
          }
        }

        // 退避等待期间若被总控制器中止，则抛出 Abort
        await new Promise<void>((resolve, reject) => {
          const t = setTimeout(() => {
            cleanup();
            resolve();
          }, delay);
          const onAbort = () => {
            cleanup();
            reject(new DOMException("The operation was aborted.", "AbortError"));
          };
          const cleanup = () => {
            clearTimeout(t);
            totalController.signal.removeEventListener("abort", onAbort);
          };
          totalController.signal.addEventListener("abort", onAbort, { once: true });
        });

        attempt += 1;
        continue;
      } finally {
        totalController.signal.removeEventListener("abort", linkAbort);
      }
    }
  } finally {
    clearTimeout(totalTimer);
    // 清理外部联动监听
    try {
      const cleanup: undefined | (() => void) = (totalController as any).__cleanupExternalLink;
      if (cleanup) cleanup();
    } catch {}
    try {
      const ext: AbortSignal | undefined = (totalController as any).__ext;
      const onAbort: any = (totalController as any).__onAbort;
      if (ext && onAbort) ext.removeEventListener("abort", onAbort);
    } catch {}
  }

  // 超出最大重试或被中止
  if (totalController.signal.aborted) {
    throw new DOMException("The operation was aborted.", "AbortError");
  }
  throw lastErr;
}

function withTraceIdHeaders(init?: RequestInit): RequestInit {
  const headers = new Headers(init?.headers || {});
  if (!headers.has("Content-Type")) headers.set("Content-Type", "application/json");
  // inject X-Trace-Id if not provided
  if (!headers.has("X-Trace-Id")) headers.set("X-Trace-Id", crypto.randomUUID());
  return { ...init, headers };
}

async function handleResponse<T>(resp: Response): Promise<T & { _traceId?: string }> {
  const traceId = resp.headers.get("X-Trace-Id") || undefined;
  const text = await resp.text();

  let json: any | undefined;
  try {
    json = text ? JSON.parse(text) : undefined;
  } catch {
    json = undefined;
  }

  if (!resp.ok) {
    const msg =
      (json && json.error && json.error.message) ||
      (text && text.trim()) ||
      resp.statusText ||
      "Request failed";
    throw new ApiError(msg, resp.status, json ?? text, traceId);
  }

  if (json !== undefined && typeof json === "object") {
    // 将 traceId 透传给调用方（显式返回）
    return Object.assign({}, json, { _traceId: traceId }) as T & { _traceId?: string };
  }
  // 非 JSON 响应也附带 _traceId
  return Object.assign({}, { value: text }, { _traceId: traceId }) as unknown as T & { _traceId?: string };
}

function safeJsonParse(text: string) {
  try {
    return JSON.parse(text);
  } catch {
    return undefined;
  }
}

export async function createSession(text: string, traceId?: string, opts?: { signal?: AbortSignal; totalTimeoutMs?: number }): Promise<CreateSessionResponse> {
  const baseInit = withTraceIdHeaders({
    method: "POST",
    body: JSON.stringify({ text }),
    headers: traceId ? ({ "X-Trace-Id": traceId } as any) : undefined,
  });
  // 网络/超时自动退避，并贯穿外部/总超时 signal
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(`${API_BASE}/sessions`, { ...baseInit, signal });
      return handleResponse<CreateSessionResponse>(resp);
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  );
}

/**
 * 发送消息（支持流式与最终响应模式）
 * - POST /api/sessions/:id/messages
 * - 优先尝试按流式 SSE 解析；若响应 Content-Type 非 text/event-stream，则按最终 JSON 解析
 * - onDelta: 逐块回调追加文本
 * - 返回：{ fullText, summaryText, _traceId? }
 */
export async function sendMessage(
  sessionId: string,
  content: string,
  opts?: SendMessageOptions
): Promise<{ fullText: string; summaryText?: string; _traceId?: string }> {
  const baseInit = withTraceIdHeaders({
    method: "POST",
    body: JSON.stringify({ content }),
  });

  // 使用总体超时包装，确保 10s 上限，并可被外部 signal 中止
  return fetchWithTimeout(async (outerSignal) => {
    // 建立与外部/总控的中止联动（抽取工具）
    const controller = new AbortController();
    const { linkAbortSignals, parseSSEStream } = await import("./sse");
    const cleanupLink = linkAbortSignals(controller, opts?.signal, outerSignal);

    try {
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(sessionId)}/messages`, {
        ...baseInit,
        signal: controller.signal,
      });

      const traceId = resp.headers.get("X-Trace-Id") || undefined;
      const ctype = resp.headers.get("Content-Type") || "";

      // 分支 A：SSE 流式
      if (resp.ok && ctype.includes("text/event-stream")) {
        const reader = (resp.body as any)?.getReader?.();
        if (!reader) {
          // 环境不支持 ReadableStream，回退为整体读取文本
          const text = await resp.text();
          // 试图解析为最终 JSON
          const json = safeJsonParse(text);
          if (json && typeof json === "object" && typeof (json as any).content === "string") {
            const fullText = String((json as any).content || "");
            if (opts?.onDelta && fullText) opts.onDelta(fullText);
            return { fullText, summaryText: (json as any).summary?.text, _traceId: traceId };
          }
          // 否则视为纯文本
          if (opts?.onDelta && text) opts.onDelta(text);
          return { fullText: text, _traceId: traceId };
        }

        type StreamData = { delta?: string; summary?: { text?: string } };
        let fullText = "";
        let summaryText: string | undefined = undefined;

        await parseSSEStream<StreamData>(reader, (evt) => {
          // 仅处理 data 字段；坏 JSON 将以 evt.raw 呈现，忽略不中断
          const data = evt.data as StreamData | undefined;
          if (!data || typeof data !== "object") return;
          const delta = typeof data.delta === "string" ? data.delta : "";
          if (delta) {
            fullText += delta;
            if (opts?.onDelta) {
              try {
                opts.onDelta(delta);
              } catch {
                /* 用户回调错误不打断流 */
              }
            }
          }
          if (data.summary && typeof data.summary.text === "string") {
            summaryText = data.summary.text;
          }
        }, controller.signal);

        return { fullText, summaryText, _traceId: traceId };
      }

      // 分支 B：最终 JSON
      const text = await resp.text();
      let json: any | undefined;
      try {
        json = text ? JSON.parse(text) : undefined;
      } catch {
        json = undefined;
      }

      if (!resp.ok) {
        const msg =
          (json && json.error && json.error.message) ||
          (text && text.trim()) ||
          resp.statusText ||
          "Request failed";
        throw new ApiError(msg, resp.status, json ?? text, traceId);
      }

      // 成功：JSON or 纯文本
      if (json && typeof json === "object") {
        const fullText = String((json as any).content || "");
        if (opts?.onDelta && fullText) opts.onDelta(fullText);
        return { fullText, summaryText: (json as any).summary?.text, _traceId: traceId };
      }
      // 非 JSON：当作纯文本一次性回放
      if (opts?.onDelta && text) opts.onDelta(text);
      return { fullText: text, _traceId: traceId };
    } finally {
      // 清理中止联动
      try {
        cleanupLink();
      } catch {
        /* no-op */
      }
    }
  }, { totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000, externalSignal: opts?.signal });
}

export async function getSessions(limit = 50, offset = 0, opts?: { signal?: AbortSignal; totalTimeoutMs?: number }): Promise<SessionListResponse & { _traceId?: string }> {
  const baseInit = withTraceIdHeaders();
  const url = new URL(`${API_BASE}/sessions`);
  url.searchParams.set("limit", String(limit));
  url.searchParams.set("offset", String(offset));
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(url.toString(), { ...baseInit, signal });
      return handleResponse<SessionListResponse>(resp);
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  );
}

export async function getSessionById(id: string, opts?: { signal?: AbortSignal; totalTimeoutMs?: number }): Promise<SessionDetailResponse & { _traceId?: string }> {
  const baseInit = withTraceIdHeaders();
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(id)}`, { ...baseInit, signal });
      return handleResponse<SessionDetailResponse>(resp);
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  );
}

// --- 新增：阅读进度 API（支持 ETag/If-Match） ---
export async function getSessionProgress(
  id: string,
  opts?: { signal?: AbortSignal; totalTimeoutMs?: number }
): Promise<SessionProgressResponse & { _meta: ProgressMeta; _traceId?: string }> {
  const baseInit = withTraceIdHeaders();
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(id)}/progress`, { ...baseInit, signal });
      const json = await handleResponse<SessionProgressResponse>(resp);
      const etag = resp.headers.get("ETag") || undefined;
      return Object.assign({}, json, { _meta: { etag }, _traceId: (json as any)._traceId }) as any;
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  );
}

function ensureDeviceId(): string {
  const key = "zhiread_device_id";
  let val = localStorage.getItem(key);
  if (!val) {
    val = crypto.randomUUID();
    localStorage.setItem(key, val);
  }
  return val;
}

export async function updateSessionProgress(
  id: string,
  progress: number,
  opts?: { ifMatch?: string; signal?: AbortSignal; totalTimeoutMs?: number }
): Promise<SessionProgressResponse & { _meta: ProgressMeta; _traceId?: string }> {
  const headers: Record<string, string> = {};
  const deviceId = ensureDeviceId();
  headers["X-Device-Id"] = deviceId;
  if (opts?.ifMatch) headers["If-Match"] = opts.ifMatch;
 
  const baseInit = withTraceIdHeaders({
    method: "PUT",
    body: JSON.stringify({ progress: Math.max(0, Math.min(100, Math.round(progress))) }),
    headers,
  });
 
  // 409 冲突不做自动重试，由调用方处理交互；其他网络/超时允许自动退避；贯穿 signal
  return retryWithBackoff(
    async (signal) => {
      const resp = await fetch(`${API_BASE}/sessions/${encodeURIComponent(id)}/progress`, { ...baseInit, signal });
      if (resp.status === 409) {
        // surface server details for conflict handling at caller level
        const text = await resp.text();
        const data = safeJsonParse(text) || { error: "conflict" };
        const err = new ApiError("Conflict", 409, data, resp.headers.get("X-Trace-Id") || undefined);
        throw err;
      }
      const json = await handleResponse<SessionProgressResponse>(resp);
      const etag = resp.headers.get("ETag") || undefined;
      return Object.assign({}, json, { _meta: { etag }, _traceId: (json as any)._traceId }) as any;
    },
    { signal: opts?.signal, totalTimeoutMs: opts?.totalTimeoutMs ?? 10_000 }
  );
}