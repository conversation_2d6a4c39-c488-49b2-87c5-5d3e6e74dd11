/**
 * SSE 工具与类型（Phase 1）
 * - linkAbortSignals：统一中止联动与清理
 * - parseSSEStream：UTF-8 解码、按事件分割、data: 聚合、JSON 安全解析
 */

export type SSEEvent<T = unknown> = {
  event?: string;
  data?: T;
  raw?: string; // 坏 JSON 或未识别文本
};

/**
 * 将多个外部 AbortSignal 链接到内部 controller.signal，任一中止即中止 controller。
 * 返回 cleanup() 以移除监听。
 */
export function linkAbortSignals(controller: AbortController, ...signals: (AbortSignal | undefined)[]): () => void {
  const onAbortMap = new Map<AbortSignal, (this: AbortSignal, ev: Event) => any>();

  const abortWithReason = (s: AbortSignal) => {
    // DOMException reason 在部分浏览器不可取，但保持兼容性调用
    try {
      (controller as any).abort(s.reason);
    } catch {
      controller.abort();
    }
  };

  for (const s of signals) {
    if (!s) continue;
    if (s.aborted) {
      abortWithReason(s);
      continue;
    }
    const onAbort = () => abortWithReason(s);
    onAbortMap.set(s, onAbort);
    s.addEventListener("abort", onAbort, { once: true });
  }

  return () => {
    for (const [s, handler] of onAbortMap.entries()) {
      try {
        s.removeEventListener("abort", handler as any);
      } catch {
        // no-op
      }
    }
    onAbortMap.clear();
  };
}

/**
 * 解析 text/event-stream
 * - 逐块解码为文本，按 \n\n 作为事件边界
 * - 支持 event: 与 data: 行，data: 可多行拼接
 * - data 为 JSON 时尝试解析，失败则以 { raw } 上报不中断
 * - signal 中止将尝试取消 reader
 */
export async function parseSSEStream<T = unknown>(
  reader: ReadableStreamDefaultReader<Uint8Array>,
  onEvent: (evt: SSEEvent<T>) => void,
  signal?: AbortSignal
): Promise<void> {
  const decoder = new TextDecoder("utf-8");
  let buf = "";

  // 若提供中止信号，则在中止时尝试取消 reader
  let aborted = false;
  const onAbort = async () => {
    aborted = true;
    try {
      await reader.cancel();
    } catch {
      // ignore
    }
  };
  if (signal) {
    if (signal.aborted) {
      await onAbort();
      throw new DOMException("The operation was aborted.", "AbortError");
    }
    signal.addEventListener("abort", onAbort, { once: true });
  }

  try {
    // 按规范，事件由若干行组成，以空行 \n\n 结束
    // 我们支持如下字段：
    // - event: <type>
    // - data: <payload>（可多行，按换行拼接）
    let pendingEventType: string | undefined;
    let pendingDataLines: string[] = [];

    const flushEvent = () => {
      if (pendingEventType === undefined && pendingDataLines.length === 0) return;
      const rawData = pendingDataLines.join("\n");
      let parsed: T | undefined;
      let raw: string | undefined;
      if (rawData.trim()) {
        try {
          parsed = JSON.parse(rawData) as T;
        } catch {
          raw = rawData;
        }
      }
      const evt: SSEEvent<T> = {};
      if (pendingEventType) evt.event = pendingEventType;
      if (parsed !== undefined) evt.data = parsed;
      if (raw !== undefined) evt.raw = raw;
      onEvent(evt);
      pendingEventType = undefined;
      pendingDataLines = [];
    };

    while (true) {
      const { value, done } = await reader.read();
      if (done) break;
      buf += decoder.decode(value, { stream: true });

      let idx: number;
      while ((idx = buf.indexOf("\n\n")) >= 0) {
        const rawEvent = buf.slice(0, idx);
        buf = buf.slice(idx + 2);
        // 按行处理
        const lines = rawEvent.split("\n");
        for (let line of lines) {
          // 去除\r 以兼容 \r\n
          if (line.endsWith("\r")) line = line.slice(0, -1);
          if (!line) continue;
          // 忽略以冒号开头的注释
          if (line.startsWith(":")) continue;

          const colon = line.indexOf(":");
          const field = colon === -1 ? line : line.slice(0, colon);
          let valueStr = colon === -1 ? "" : line.slice(colon + 1);
          if (valueStr.startsWith(" ")) valueStr = valueStr.slice(1);

          if (field === "event") {
            pendingEventType = valueStr || undefined;
          } else if (field === "data") {
            pendingDataLines.push(valueStr);
          } else {
            // 其他字段忽略（如 id, retry）
          }
        }
        // 事件结束，触发一次
        flushEvent();
      }
    }
    // 末尾残留（未以 \n\n 结尾）不视为完整事件，忽略
    if (aborted) {
      throw new DOMException("The operation was aborted.", "AbortError");
    }
  } finally {
    if (signal) {
      try {
        signal.removeEventListener("abort", onAbort as any);
      } catch {
        // ignore
      }
    }
  }
}