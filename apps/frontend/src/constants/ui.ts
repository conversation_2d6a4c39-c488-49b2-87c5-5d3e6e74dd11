// 统一 UI 常量与文案（Story 1.7/1.10/1.12）
export const skeletonMinDurationMs = 300; // 避免骨架闪烁
export const emptyMinViewMs = 300; // 空态最小展示时长
export const longLoadingMs = 10_000; // 超过 10s 仍在加载的提醒阈值（Story 1.10）

export const errorTexts = {
  commonTitle: "加载失败，请重试",
  traceIdPrefix: "问题追踪ID：",
  actions: {
    retry: "重试",
    report: "反馈",
    back: "返回",
    create: "新建",
    home: "回首页",
  },
  // Story 1.10：错误类型映射到统一文案 key
  types: {
    network: { title: "网络异常", subtitle: "请检查网络连接，或稍后重试" },
    timeout: { title: "请求超时", subtitle: "网络较慢或服务繁忙，请重试" },
    unauthorized: { title: "未授权", subtitle: "请返回首页重新开始或创建新会话" },
    notfound: { title: "内容不存在", subtitle: "资源未找到，可能已被移除" },
    server: { title: "服务器异常", subtitle: "服务暂时不可用，请稍后重试" },
    unknown: { title: "发生未知错误", subtitle: "请重试或反馈问题，我们会尽快处理" },
  },
  // a11y 文案
  aria: {
    errorRegionLabel: "错误信息",
    longLoading: "仍在加载，请稍候",
  },
};

export const emptyTexts = {
  library: {
    title: "还没有任何学习会话",
    subtitle: "从一个文本开始，创建你的第一条学习会话",
    cta: "去创建第一条学习会话",
  },
  session: {
    title: "暂无内容",
    subtitle: "请返回并选择其他会话，或稍后重试",
  },
  reader: {
    title: "没有可显示的内容",
    subtitle: "请返回会话列表或刷新后重试",
  },
  // 摘要区块空态（Story 1.9/1.10）
  summary: {
    title: "暂无摘要",
    subtitle: "当前会话尚未生成摘要，稍后可在学习会话页查看或刷新。",
  },
};

// Story 1.12：新增对话与摘要相关常量
export const streamingTexts = {
  loadingPlaceholder: "生成中...",
  // Phase 1: 新增长加载轻提示键，供 Reader/Placeholders 统一引用
  loading: "正在加载内容…",
  sendButton: {
    idle: "发送",
    loading: "生成中...",
    cancel: "取消",
  },
  a11y: {
    assistantLiveRegion: "AI 回答正在生成",
  },
};

export const summaryTexts = {
  updatedHint: "摘要已更新",
  // Phase 1: UI 轻提示键，Reader 在摘要更新时短暂展示
  updated: "摘要已更新",
};

export function buildReportMailto(traceId?: string) {
  const subject = encodeURIComponent("Zhiread 问题反馈");
  const body = encodeURIComponent(
    `我在使用 Zhiread 时遇到问题。\n\ntrace_id: ${traceId || "-"}\nURL: ${location.href}\nUA: ${navigator.userAgent}\n\n请协助排查，谢谢。`
  );
  return `mailto:<EMAIL>?subject=${subject}&body=${body}`;
}