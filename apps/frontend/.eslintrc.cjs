// ESLint config for TypeScript + React
module.exports = {
  root: true,
  env: { browser: true, es2021: true, node: true },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
    ecmaFeatures: { jsx: true }
  },
  settings: {
    react: { version: "detect" },
    "import/resolver": {
      typescript: {
        // Use project's tsconfig.json
        project: "./tsconfig.json"
      }
    }
  },
  plugins: ["@typescript-eslint", "react", "react-hooks", "jsx-a11y", "import"],
  extends: [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended",
    "plugin:import/recommended",
    "plugin:import/typescript",
    "prettier"
  ],
  overrides: [
    {
      files: ["*.ts", "*.tsx"],
      rules: {
        // Allow TypeScript to handle unused vars but ignore underscores
        "@typescript-eslint/no-unused-vars": ["warn", { argsIgnorePattern: "^_", varsIgnorePattern: "^_" }],
        // Relax explicit any in tests and quick scaffolding
        "@typescript-eslint/no-explicit-any": "off"
      }
    },
    {
      files: ["**/*.test.ts", "**/*.test.tsx", "src/test/**"],
      env: { jest: true },
      rules: {
        "@typescript-eslint/no-explicit-any": "off"
      }
    }
  ],
  rules: {
    "react/react-in-jsx-scope": "off"
  }
};
module.exports = {
  root: true,
  env: { browser: true, es2021: true, node: true },
  extends: ["eslint:recommended", "plugin:react/recommended"],
  parserOptions: { ecmaVersion: "latest", sourceType: "module" },
  settings: { react: { version: "detect" } },
  rules: {
    "react/react-in-jsx-scope": "off"
  }
};