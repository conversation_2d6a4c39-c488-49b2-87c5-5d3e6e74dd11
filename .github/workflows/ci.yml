name: CI

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches:
      - "**"

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHON_VERSION: "3.11"
  NODE_VERSION: "18.x"

jobs:
  lint-test-build:
    name: Lint • Test • Build
    runs-on: ubuntu-latest
    timeout-minutes: 25

    strategy:
      fail-fast: false
      matrix:
        part: [frontend, backend]

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        if: ${{ matrix.part == 'frontend' }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "pnpm"

      - name: Setup pnpm
        if: ${{ matrix.part == 'frontend' }}
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false

      - name: Frontend install
        if: ${{ matrix.part == 'frontend' }}
        working-directory: apps/frontend
        run: |
          pnpm install
          pnpm -v
          node -v

      - name: Frontend lint
        if: ${{ matrix.part == 'frontend' }}
        working-directory: apps/frontend
        run: pnpm lint

      - name: Frontend test
        if: ${{ matrix.part == 'frontend' }}
        working-directory: apps/frontend
        run: pnpm test -- --ci --reporter=default

      - name: Frontend build
        if: ${{ matrix.part == 'frontend' }}
        working-directory: apps/frontend
        run: pnpm build

      - name: Setup Python
        if: ${{ matrix.part == 'backend' }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "pip"

      - name: Backend install
        if: ${{ matrix.part == 'backend' }}
        working-directory: apps/backend
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          # Ensure dev tools
          pip install ruff black pytest alembic

      - name: Backend lint (Ruff)
        if: ${{ matrix.part == 'backend' }}
        working-directory: apps/backend
        run: |
          ruff check app/ tests/

      - name: Backend format check (Black)
        if: ${{ matrix.part == 'backend' }}
        working-directory: apps/backend
        run: |
          black --check app/ tests/

      - name: Backend tests (Pytest)
        if: ${{ matrix.part == 'backend' }}
        working-directory: apps/backend
        run: |
          pytest -q

      - name: Backend migration placeholder (Alembic)
        if: ${{ matrix.part == 'backend' }}
        working-directory: apps/backend
        env:
          # Use SQLite for placeholder; real Postgres should be configured in env for integration runs
          DATABASE_URL: "sqlite:///./test.db"
        run: |
          echo "Alembic upgrade placeholder (skipped without real DB)."
          # Uncomment when DB is available:
          # alembic upgrade head

      - name: Backend build (placeholder)
        if: ${{ matrix.part == 'backend' }}
        working-directory: apps/backend
        run: |
          echo "No build step required for FastAPI; ensure Docker build succeeds in release job."

  preview:
    name: Preview Deploy (placeholder)
    needs: [lint-test-build]
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'pull_request' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Preview placeholder
        run: |
          echo "Integrate with Vercel/Netlify for FE, Render/Fly.io for BE."
          echo "Add secrets and environment configs in repository settings."

  release:
    name: Release Deploy (placeholder)
    needs: [lint-test-build]
    runs-on: ubuntu-latest
    if: ${{ github.ref == 'refs/heads/main' && github.event_name == 'push' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Release placeholder
        run: |
          echo "Integrate production deployment with required secrets and env matrices."
          echo "Protect main branch and require CI to pass before merge."