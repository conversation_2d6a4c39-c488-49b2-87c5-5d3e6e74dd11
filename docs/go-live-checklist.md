# 上线前准备清单（Go-Live Checklist）

目的：在 MVP 上线前，明确人类（PO/DevOps/Owner）需要完成的前置准备，降低发布风险。

关联文档：[`README.md`](README.md)｜[`docs/architecture.md`](docs/architecture.md)｜[`docs/prd.md`](docs/prd.md)｜[`docs/ui-ux.md`](docs/ui-ux.md)｜[`docs/openapi.yaml`](docs/openapi.yaml)

---

## 1. 账户与权限（人类职责）

- [ ] GitHub 组织与仓库权限已配置（最小权限，保护 main 分支）
- [ ] Vercel/Netlify 项目已创建（前端），成员与权限到位
- [ ] Render/Fly.io 项目已创建（后端），成员与权限到位
- [ ] Supabase 项目已创建（PG/Auth/Storage），RLS/Policies 不走空白默认
- [ ] Sentry 组织与项目已创建（FE/BE），Dsn 已可用
- [ ] 计费方式已绑定至各平台（配额/账单告警开启）

## 2. Secrets 与环境变量

- [ ] 根目录已有 [.env.example](../.env.example)，键名与实际部署保持一致
- [ ] GitHub Secrets（仓库/环境）已配置：
  - [ ] SUPABASE_URL / SUPABASE_ANON_KEY / SUPABASE_SERVICE_ROLE_KEY
  - [ ] DATABASE_URL
  - [ ] LLM_API_KEY / LLM_API_BASE / LLM_DEFAULT_MODEL
  - [ ] SENTRY_DSN
  - [ ] 其他 CI_DEPLOY_TOKEN / RELEASE_VERSION（流水线注入）
- [ ] 部署平台（Vercel/Render 等）已配置等价环境变量，并与分支环境绑定（preview / production）
- [ ] Secrets 轮转流程记录（负责人、周期、异常处理）

## 3. 基础设施与安全

- [ ] Supabase RLS 策略启用，核心表（sessions/messages/summaries）已按 user_id 限制
- [ ] 数据库索引创建（sessions(user_id, created_at), messages(session_id, created_at)）
- [ ] 备份策略（快照/保留期）已配置并验证恢复演练
- [ ] 后端 CORS 白名单配置正确（仅允许预期域名）
- [ ] 速率限制策略上线（IP/User，窗口与阈值与文档一致）
- [ ] TLS/HTTPS 全链路启用（前后端均强制）

## 4. 监控与告警

- [ ] Sentry 前后端已接入并验证事件上报（含 trace_id）
- [ ] 指标看板已建立（API 请求率/错误率/延迟；LLM token/延迟/错误率；成本预算）
- [ ] 告警阈值设置并测试通知渠道（Slack/Email）
- [ ] 日志保留策略与检索方式可用（含trace_id查询）

## 5. 构建与发布

- [ ] CI 工作流（GitHub Actions）绿灯：Lint/Test/Build 通过
- [ ] main 分支保护规则启用（必须 PR + 通过检查）
- [ ] 预览部署（develop/PR）可用并可供验收
- [ ] 生产部署流程演练（包含回滚步骤）
- [ ] 版本标识与变更记录（CHANGELOG 或 Release Notes）

## 6. 功能验收（对照 PRD/EPIC）

- [ ] FR1–FR9 全部通过（参见 [`docs/prd.md`](docs/prd.md) 5.1）
- [ ] Epic 里程碑 M1–M5 验收项通过（参见 [`docs/epic-core-mvp.md`](docs/epic-core-mvp.md) 6）
- [ ] UI/UX 关键交互（双栏/流式/摘要更新/定位）符合规范（[`docs/ui-ux.md`](docs/ui-ux.md)）
- [ ] 错误模型统一（error.code/message/details/trace_id）并在 UI 友好呈现

## 7. 降级与回退

- [ ] 已阅读并启用《LLM 降级与回退策略表》（[`docs/llm-degrade-matrix.md`](docs/llm-degrade-matrix.md)）
- [ ] 降级开关与参数可在运行时调整（环境变量/配置服务）
- [ ] 备选模型或提供商配置可用，切换流程已演练
- [ ] 回滚流程（前端/后端/数据库迁移）可在 15 分钟内完成

## 8. 法务与合规（如适用）

- [ ] 隐私政策/服务条款草案
- [ ] 第三方条款合规（LLM/平台/SDK）
- [ ] 日志/数据保留与用户数据删除流程

## 9. 文档与支持

- [ ] README 快速开始准确
- [ ] OpenAPI v0（[`docs/openapi.yaml`](docs/openapi.yaml)）与实现一致
- [ ] Runbook（运维手册）条目在仓库可见
- [ ] 支持渠道（Issue 模板/联系方式）可用

---

## 最终签署（Go/No-Go）

- 技术负责人（架构/后端）：签名/日期：
- 前端负责人：签名/日期：
- 产品负责人（PO）：签名/日期：
- 运营/支持负责人：签名/日期：

决策：APPROVE / CONDITIONAL / REJECT
条件（如 CONDITIONAL）：…