# 史诗（Epic）：核心对话式学习体验 MVP

作者：John（PM）  
日期：2025-08-06  
关联文档：PRD([`docs/prd.md`](docs/prd.md))，架构([`docs/architecture.md`](docs/architecture.md))，前端规范([`docs/ui-ux.md`](docs/ui-ux.md))

---

## 1. 目标（Goals）

1) 交付一个“粘贴文本 → 对话式学习 → 自动保存/恢复”的完整闭环，以验证“动态上下文管理 + 可交互摘要”的核心价值。  
2) 实现左对话｜右内容（原文/摘要切换）的专注双栏学习体验，支持多轮对话与摘要的最小可用更新。  
3) 建立可运维与可演进的技术基线（统一错误模型、trace_id 贯穿、流式响应、重试与超时、基本观测与告警）。

业务价值（Business Value）：  
- 提升深度理解效率（减少上下文管理的认知负荷）。  
- 提高留存与使用时长（沉浸式学习体验）。  
- 验证用户愿意为“更深、更可信的理解”投入时间（为商业化与后续功能奠基）。

---

## 2. 范围（Scope）

2.1 In Scope（MVP）  
- 纯文本粘贴导入创建学习会话（POST /api/sessions）。  
- 学习会话视图（Session）：左对话区，右内容画布（原文/摘要Tab切换）。  
- 动态摘要最小可用：AI回复后自动更新摘要结构；摘要Tab显示“已更新”提示；摘要节点点击可定位到对应的对话消息。  
- 自动保存与断点续学：会话、消息、摘要、阅读位置持久化与恢复。  
- 统一错误模型与trace_id贯穿；流式响应；客户端指数退避重试；服务端合理超时；基础告警。

2.2 Out of Scope（MVP）  
- 多格式导入（URL/PDF/视频等）与抓取清洗。  
- 记忆熔炉（间隔重复/卡片）与知识网络化/跨文档连接。  
- 摘要节点直接回溯原文的链路（后MVP补齐）；语音学习/播客；协作与分享。

---

## 3. 用户故事（User Stories）

Story 1.1 项目基础架构与会话创建 API  
- 作为开发者，我需要初始化前后端骨架，并提供 POST /api/sessions 接口，可接收纯文本并创建会话。  
验收标准：  
- FE/BE 项目初始化，CI 基线通过；DB 建立 sessions 表。  
- 成功调用接口后返回 id/title/created_at；列表可见该会话。  
与架构对齐：[`docs/architecture.md`](docs/architecture.md) 6.2, 3, 4

Story 1.2 知识库 UI 与文档列表  
- 作为用户，我想在“知识库（Library）”中查看我的会话列表，并从此处粘贴文本创建新会话。  
验收标准：  
- 三栏布局；左侧“收件箱/文档/标签”，中间列表，右侧预览空态/引导。  
- 顶部“粘贴内容创建会话”入口，成功创建后列表更新。  
与前端规范对齐：[`docs/ui-ux.md`](docs/ui-ux.md) 2.1, 3.2

Story 1.3 阅读视图与学习会话转换  
- 作为用户，我希望在阅读视图中点击“与导师学习”无缝进入双栏学习会话视图。  
验收标准：  
- Reader 顶部按钮；点击后进入 Session（左对话右内容），保留阅读位置。  
与前端规范对齐：[`docs/ui-ux.md`](docs/ui-ux.md) 2.2, 2.3

Story 1.4 核心对话交互与动态摘要  
- 作为用户，我希望与AI进行多轮对话，并看到摘要自动更新，摘要节点可定位到对应的对话消息。  
验收标准：  
- POST /api/sessions/{id}/messages 支持流式响应；AI回复展示在左栏。  
- 右栏摘要Tab以徽标提示“已更新”；摘要节点点击定位并高亮对应消息。  
- 右栏可在“原文/摘要”切换。  
与架构/前端规范对齐：[`docs/architecture.md`](docs/architecture.md) 3, 7；[`docs/ui-ux.md`](docs/ui-ux.md) 3.5, 3.3, 4

Story 1.5 会话持久化与断点续学  
- 作为用户，我希望关闭页面后再次进入能够恢复上次的对话、摘要与阅读位置。  
验收标准：  
- GET /api/sessions/:id 返回 messages/summary/reading_position；前端恢复UI偏好与滚动位置。  
与架构/前端规范对齐：[`docs/architecture.md`](docs/architecture.md) 4, 7；[`docs/ui-ux.md`](docs/ui-ux.md) 4.3

---

## 4. 依赖（Dependencies）

- 外部：Supabase（PG/Auth/Storage）、LLM Provider（OpenAI/Claude/Gemini等）、Sentry、Vercel/Render。  
- 内部：PRD 与前端规范、全栈架构文档、设计令牌。  
- 团队：PM/BA、UX、FE、BE、QA；CI/CD 管理与Secrets配置。

---

## 5. 风险（Risks）与缓解

- 动态摘要质量不稳定（高）：采用最小可解释结构，逐步灰度优化提示与策略；保留“一键回到原文/对话”锚点。  
- 成本不可控（高）：对LLM调用采用流式、请求合并与基础缓存；设预算阈值与告警；必要时启用分层模型策略。  
- 可靠性（中）：对外部调用设置超时与有限重试；客户端指数退避；关键路径增加日志与指标。  
- 可用性门槛（中）：首次引导与轻量模式，降低上手成本。

---

## 6. 里程碑与验收标准（Milestones & Acceptance）

M1（周1-2）：项目骨架与基础设施  
- FE/BE 初始化；CI/Lint/Test 基线；Sentry 接入；.env.example。  
- 验收：流水线绿灯，错误上报可见，Secrets配置就绪。

M2（周2-3）：会话创建与列表展示  
- POST /api/sessions；DB 表；Library 列表与粘贴创建入口。  
- 验收：创建后返回ID并展示于列表；统一错误模型回传 trace_id。

M3（周3-4）：阅读视图与会话视图切换  
- Reader → Session 的平滑过渡；阅读位置记忆。  
- 验收：切换可靠、状态持久；空态/错误态符合规范。

M4（周4-5）：对话与摘要最小可用  
- 流式回复；摘要更新提示；摘要节点定位对话。  
- 验收：多轮对话稳定；摘要更新率≥90%会话生效；Tab切换记忆滚动。

M5（周5-6）：断点续学与观测/可靠性基线  
- 状态恢复完整；重试/超时/速率限制；基础告警。  
- 验收：刷新/重登恢复成功率≥95%；P50 TTFB<1s、P50 AI响应<3s；错误率、P99 延迟告警规则生效。

---

## 7. 指标（Metrics）

北极星指标：  
- 每周深度学习时长（含有效对话轮次、摘要交互次数）。

产品指标（MVP目标）：  
- 次日留存（激活会话后）≥35%。  
- 单会话平均有效对话轮次≥6。  
- 摘要交互使用率≥50% 的会话出现。  
- 历史会话恢复率与恢复后的继续学习时长提升。

技术指标：  
- API 请求率、错误率、延迟（P50/P90/P99）。  
- LLM 调用次数、Token 消耗、延迟、错误率。  
- 成本预算告警次数与处置时长。

---

## 8. 验收清单（DoD）

- 功能：FR1–FR9 全部通过；端到端闭环可演示。  
- 可靠性：客户端重试、服务端超时、速率限制配置完备；错误模型一致。  
- 可观测：Sentry + 结构化日志；指标面板与告警生效；trace_id 贯穿 FE/BE。  
- 文档：更新 PRD/架构/前端规范；README 与运行手册条目齐备。  
- 测试：关键路径单测/集成覆盖；可访问性与性能基线达标。

（Epic 完）