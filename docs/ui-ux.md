# 前端规范文档（BMaD 完整版）V2.1：知深学习导师

作者：Sally（UX Expert）  
日期：2025-08-06  
关联文档：项目简报([`docs/brief.md`](docs/brief.md))，PRD([`docs/prd.md`](docs/prd.md))，架构([`docs/architecture.md`](docs/architecture.md))

---

## 1. 信息架构（Information Architecture）

1.1 站点地图（核心应用域）  
- 知识库（Library）
  - 收件箱（Inbox）
  - 文档库（Documents）
  - 标签/过滤（Labels/Filters）
- 内容阅读视图（Reader）
- 学习会话视图（Session）
- 设置（Settings）

1.2 导航与可发现性  
- 顶部导航：仅保留产品Logo、帮助/快捷键入口（Cmd/Ctrl + /）、用户菜单（后MVP可加）。  
- 侧边栏（Library View 左侧栏）：固定分组（收件箱、我的文档、标签）。  
- 面包屑：Reader/Session 顶部显示 文档标题（可点击返回列表），当前视图状态（原文/摘要）。  
- 搜索与过滤：Library 中间栏顶部固定搜索框与过滤控件。

1.3 URL 结构与路由（示例）  
- /library  
- /library?filter=tag:research  
- /doc/:sessionId/reader  
- /doc/:sessionId/session

---

## 2. 页面规格（Page Specs）

2.1 知识库（Library）  
布局：三栏布局  
- 左栏（240px 固定宽）：导航（收件箱/文档/标签）、新建入口（粘贴文本卡片入口）。  
- 中栏（弹性宽）：文档列表（卡片/行式两种展示切换，MVP默认行式）。  
- 右栏（480px 最小宽）：默认空态（欢迎/引导）；选中文档后进入 Reader 预览或完整阅读。

关键交互：  
- 新建会话入口：右栏/中栏顶部展示“粘贴内容创建学习会话”的大按钮/区域。  
- 列表项：标题、来源（手动/粘贴）、创建时间、摘要预览（可截断）、标签。  
- 快捷操作：Hover 行展示“开始学习（跳到 Session）/仅阅读（Reader）/重命名/删除”。  
空态：无文档时显示引导卡片（粘贴创建 + 键盘提示 + 示例内容导入）。

2.2 内容阅读视图（Reader）  
布局：单栏阅读区，右侧窄栏展示文档大纲（后MVP）。顶部固定栏：文档标题、返回Library、“与导师学习”主按钮。  
内容区域：排版优化（参见第6节主题/排版），支持基础高亮（后MVP）。  
关键交互：  
- 点击“与导师学习”：平滑切换进入 Session 视图。  
- 滚动记忆：保存阅读位置（进入 Session 后仍保留，回 Reader 可恢复）。  
空态：加载中骨架屏；加载失败错误提示（统一错误模型）。

2.3 学习会话视图（Session）  
布局：双栏  
- 左栏（对话区，720-880px）：对话历史（用户/AI气泡）、输入区（多行、回车发送/Shift+回车换行）、发送按钮。  
- 右栏（内容画布，480-640px）：Tab 切换“原文 / 动态摘要”。  
关键交互：  
- 流式响应：AI消息逐字流式输出；显示“思考占位”与取消按钮（可选）。  
- 摘要更新：每次AI回复后，右栏摘要Tab徽标闪烁/标记“已更新”，点击查看差异（最小提示）。  
- 摘要节点交互：可展开/折叠；点击节点定位到对应对话（MVP）；后MVP支持定位原文。  
- 状态持久化：切换Tab状态、滚动位置、输入草稿；刷新后恢复。  
空态：首次进入会话时右栏默认“原文”视图（可由实验调整默认态）。

---

## 3. 组件规范（Component Specs）

3.1 侧边栏导航 SidebarNav  
属性：sections（固定项）、tags（动态标签），activeItem。  
交互：  
- 点击项切换过滤；当前项高亮。  
- 标签支持长列表滚动；右键菜单（后MVP）编辑标签。  
空态：无标签时显示“创建标签”引导（后MVP）。

3.2 文档列表项 DocumentListItem  
内容：标题、来源（Text/Paste）、创建时间、摘要预览、标签。  
交互：  
- Hover 出操作：阅读、开始学习、更多。  
- 点击项：在右栏加载 Reader 预览；双击项：进入 Reader 全屏。  
骨架：标题行+两行副文本的骨架占位。

3.3 内容画布 Tabs（原文/摘要）  
属性：activeTab（'source'|'summary'），updatedBadge（boolean）。  
交互：  
- 切换Tab：保持滚动位置独立记忆。  
- 更新态：在AI回复后短暂展示“更新”徽标。  
空态：  
- 原文：无内容时显示“请先在Library中选择或创建会话”。  
- 摘要：首次无摘要时显示“开始与导师对话以生成摘要”。

3.4 对话输入与气泡（ChatInput, ChatMessage）  
输入：多行文本框；快捷键提示；发送按钮 disabled 条件（空输入）。  
消息气泡：用户与AI区分；AI流式占位及“停止”按钮；失败重试按钮。  
错误气泡：使用统一错误结构渲染，显示对用户友好的 message 与重试。

3.5 摘要节点（SummaryNode）  
结构（MVP建议）：  
- 层级：主题（H1）→要点（H2）→细节（H3，可选）。  
- 字段：id、title、bullets[]、linkedMessageIds[]。  
交互：  
- 展开/折叠；点击定位对话（滚动到对应消息并高亮1.5秒）。  
- 复制：复制节点文本（后MVP）。  
可视：缩进与引导线；选中态高亮。

---

## 4. 交互与状态（Interactions & States）

4.1 键盘交互  
- 全局：Cmd/Ctrl + K 打开命令面板（后MVP）；Cmd/Ctrl + / 打开快捷键帮助。  
- 输入框：Enter 发送，Shift+Enter 换行；Esc 取消/清空（确认）。  
- 列表导航：上下箭头选中列表项，Enter 打开右侧预览。

4.2 加载/空态/错误态  
- 加载：骨架屏 + 线性进度条（适度）。  
- 空态：场景化引导 + 示例内容导入按钮（MVP可提供静态示例）。  
- 错误：统一错误组件，显示 code/message，可展开 details/trace_id；提供“重试”和“复制诊断信息”。

4.3 持久化状态  
- Local：UI偏好（列表视图模式、摘要默认Tab）、滚动位置、输入草稿（会话级）。  
- Server：会话/消息/摘要/阅读位置。  
- 恢复策略：优先使用服务器状态，缺失时回退本地缓存。

---

## 5. 可访问性（Accessibility，WCAG 2.1 AA）

- 语义：标题层级、nav/section/main/aside 正确使用。  
- 对比度：文本与交互控件对比度≥4.5:1。  
- 键盘：Tab 顺序合理，焦点可见，所有核心操作可键盘完成。  
- ARIA：为动态摘要更新、流式输出、错误提示提供 ARIA live 区域与角色。  
- 读屏：图标附加 aria-label；摘要节点展开/折叠状态可读。  
- 动作大小：可点击目标≥44px。  
- 动效：尊重“减少动态效果（prefers-reduced-motion）”系统设置。

---

## 6. 响应式与主题（Responsive & Theming）

- 响应式断点：  
  - ≥1440px：三栏宽屏优化；Session 左栏更宽、右栏最大640px。  
  - 1024–1439px：标准桌面布局（默认）。  
  - 768–1023px：Library 简化为两栏（左侧抽屉 + 列表/阅读），Session 仍保持双栏（右栏可折叠）。  
  - <768px：MVP不强制适配；可显示“建议使用桌面端”。  
- 主题：浅色/深色；色板采用中性低饱和度，强调排版与留白；语义色（信息/成功/警告/错误）。  
- 排版：主字体 Inter/Source Han Sans 或替代；正文 14–16px，行距 1.6；阅读视图优化段落间距与首行缩进（中文可选）。

---

## 7. API 契约（API Contracts，前端视角）

统一前缀：/api  
- POST /api/sessions  
  - req: { text: string }  
  - res: { id: string, title: string, created_at: string }  
- GET /api/sessions  
  - res: { items: Array<{ id: string, title: string, created_at: string, preview?: string, tags?: string[] }> }  
- GET /api/sessions/:id  
  - res: { id, title, created_at, messages: Message[], summary?: Summary }  
- POST /api/sessions/:id/messages  
  - req: { content: string }  
  - res (streaming preferred): chunked text 或最终 { message: Message, summary?: Summary }

错误返回（统一）：参见附录或架构文档第5.2节（[`docs/architecture.md`](docs/architecture.md):170）

---

## 8. 数据模型与类型（FE Shared Types）

Message  
- id: string  
- role: 'user' | 'assistant'  
- content: string  
- created_at: string

Summary（最小可用）  
- id: string  
- session_id: string  
- version: number  
- text: string  
- structured_content?: {  
  - sections: Array<{ id: string; title: string; bullets: string[]; linkedMessageIds?: string[] }>  
}  
- created_at: string

Session  
- id: string  
- title: string  
- active_chunk_index?: number  
- created_at: string

前端状态存储建议：Zustand/Redux（MVP可用Zustand），区分服务器实体与UI本地状态。

---

## 9. 错误处理与追踪（Errors & Tracing）

- 统一错误模型：error.code、error.message、error.details、error.trace_id。  
- UI分层处理：  
  - 非阻断错误：以 Toast/Inline 展示。  
  - 阻断错误：对话区气泡化展示，可重试。  
- 追踪：前端生成/注入 trace_id（若服务端未提供），贯穿日志与上报；Sentry 接入前端。  
- 网络重试：指数退避（初始500ms，最大8s，最多3次），仅对 GET/非幂等安全场景谨慎配置。

---

## 10. 性能与SEO（Performance & SEO）

- 性能：  
  - 首屏资源拆分（路由级代码分割）；  
  - 懒加载右栏摘要；  
  - 流式渲染对话响应；  
  - 列表虚拟化（>100项时启用）；  
  - 图片/图标使用 SVG Sprite 或 Icon 字体。  
- 缓存：  
  - 会话详情短期内内存缓存；  
  - 非个性化内容可考虑HTTP缓存策略（后端配合）。  
- SEO：  
  - MVP为登录后应用，可忽略SEO，仅保留基础Meta与PWA（后续）。

---

## 11. 测试清单（Testing Checklist）

单元/组件（Vitest + RTL）  
- SidebarNav 渲染/高亮/点击回调  
- DocumentListItem Hover 操作显示与触发  
- ChatInput 快捷键与禁用态  
- ChatMessage 流式渲染与错误重试渲染  
- Tabs 切换状态与更新徽标

集成  
- 创建会话流程（粘贴→POST→列表可见→Reader/Session切换）  
- 对话发送→流式接收→摘要更新提示  
- 状态持久化（刷新后恢复对话/摘要/阅读位置）

可访问性  
- 键盘导航覆盖核心流程  
- ARIA 属性与 live region 生效  
- 对比度与可见焦点

性能  
- 首屏加载 < 2s（在本地/预览环境基线）  
- 长列表渲染帧率 > 50fps（典型数据量）

---

## 12. 可交付物（Deliverables）

- 交互原型（Figma/类似）：Library/Reader/Session 主流程与关键组件交互动画  
- UI样式变量表（tokens）：颜色、间距、字号、阴影、圆角、动效时长  
- 组件清单与属性定义（用于 FE 实现/Storybook）  
- 可用性测试脚本（MVP核心任务）与发现记录模板  
- 无障碍审核清单（WCAG 2.1 AA 对应项）  
- 文案规范与占位文案（Empty/Error/Loading）

---

## 13. 设计令牌（Design Tokens，建议初稿）

颜色（示例）  
- 背景：--bg: #0B0C10（深色） / #FFFFFF（浅色）  
- 文本：--text: #E5E7EB（深色） / #0F172A（浅色）  
- 强调：--accent: #22D3EE  
- 辅助：--muted: #9CA3AF  
- 语义：--info: #60A5FA, --success: #34D399, --warn: #FBBF24, --error: #F87171

间距（px）：4, 8, 12, 16, 24, 32, 48  
圆角：4, 8, 12  
阴影：xs/sm/md/lg（深浅主题分离）  
动效：--ease-default: cubic-bezier(0.2, 0.0, 0, 1); --dur-fast: 120ms; --dur-base: 200ms

---

## 14. 术语与定义（Glossary）

- 动态上下文管理：以会话材料为中心，持续维护相关上下文的机制。  
- 可交互摘要：可点击/展开并可追溯到对话或原文的结构化摘要（MVP实现摘要→对话回溯）。  
- 学习会话：围绕特定材料的持续学习上下文集合（消息、摘要、阅读位置等）。

---

## 15. 变更日志（Changelog）

- 2025-08-06 V2.1：采用BMaD前端规范完整目录，全面对齐PRD/架构/简报；新增API契约、错误与追踪、性能与测试清单等落地细则。

（前端规范 V2.1 完）