# 全栈架构文档（BMaD 完整版）V2.2：知深学习导师

作者：Winston（Architect）  
日期：2025-08-06  
关联文档：简报([`docs/brief.md`](docs/brief.md))，PRD([`docs/prd.md`](docs/prd.md))，前端规范([`docs/ui-ux.md`](docs/ui-ux.md))

---

## 0. 引言（Introduction）

0.1 文档目的  
定义“知深学习导师”的全栈目标架构，作为技术真相来源（SSOT），指导MVP实现与后续演进。覆盖系统视图、逻辑与部署视图、数据视图、接口契约与横切关注点（安全、可观测、成本/性能），并给出运行手册与风险/决策记录。

0.2 范围与读者  
- 范围：前端（Vite+React SPA）、后端（FastAPI 单体）、基础设施（Supabase PG/Auth/Storage + 托管平台）、LLM适配层。  
- 读者：架构/后端/前端工程师、QA、DevOps、PM/UX。

0.3 架构动因（来自PRD/简报）  
- 核心体验：动态上下文管理与可交互摘要，支撑长程有记忆的深度对话学习。  
- UI心智模型：左对话｜右内容（原文/摘要切换），专注、极简。  
- 技术策略：MVP期以 BaaS（Supabase）提速；通过适配层保持可迁移性。  
- 非目标（MVP）：多格式导入、知识图谱、全语音/播客、协作分享等。

0.4 变更日志  
- 2025-08-06 V2.2：升级为BMaD完整目录，细化系统/逻辑/部署/数据视图，补充错误与追踪、成本与可靠性基线与运行手册，收敛与 PRD/前端规范一致。

---

## 1. 系统视图（C4：System Context）

1.1 上下文与边界  
- 使用者：登录用户（知识工作者/学生）。  
- 外部系统：LLM Provider（OpenAI/Claude/Gemini 等，可插拔）、身份认证（Supabase Auth）、对象存储（Supabase Storage）、数据库（Supabase PG）。  
- 系统边界：前端SPA仅经后端API访问数据；认证仅前端直连Auth用于登录/刷新。

1.2 系统关系图  
```mermaid
graph TD
    U[用户] -->|浏览器| FE[React SPA]
    FE -->|HTTPS/JSON| BE[FastAPI 后端]
    FE -.->|OAuth/JWT| AUTH[Supabase Auth]
    BE --> DB[(PostgreSQL via Supabase)]
    BE --> STG[Supabase Storage]
    BE --> LLM[LLM Providers via Adapter]
```

关键约束  
- 前端禁止直连数据库/存储。  
- 认证SDK仅用于登录/刷新，业务数据均走后端。  
- 后端是唯一业务编排与数据访问入口。

---

## 2. 逻辑视图（Logical View）

2.1 分层与模块  
- Web 层（FastAPI API）：路由/序列化/鉴权门面。  
- 应用服务层（Services）：会话编排、上下文组装、摘要生成与更新、错误与追踪注入。  
- 领域/仓库层（Domain/Repository）：Sessions、Messages、Summaries 等实体与仓库接口（抽象 CRUD）。  
- 适配器层（Adapters）：DB（PG/Alembic）、Auth（Supabase JWT 校验）、Storage（对象存储）、LLM（Provider 抽象）。  
- 前端客户端（apiClient）：统一请求、错误模型、trace_id 注入、指数退避重试。

2.2 核心用例流（MVP）  
- 创建会话：粘贴文本→POST /api/sessions→持久化会话→返回ID→Library 列表可见。  
- 阅读→学习：Reader 顶部“与导师学习”→进入 Session（左对话右内容）。  
- 对话与摘要：POST /api/sessions/{id}/messages（流式）→服务层调用LLM→更新摘要→返回消息与摘要→右栏摘要提示更新→可切换原文/摘要。  
- 断点续学：GET /api/sessions/{id}→恢复消息、摘要、阅读位置与UI偏好。

2.3 横切逻辑  
- 错误与统一响应：error.code/message/details/trace_id。  
- Trace：每请求注入 trace_id，贯穿日志、错误与前端上报。  
- 重试策略：客户端指数退避（网络/5xx），服务端对外部调用设定超时与有限重试。  
- 速率限制与配额：按用户或IP节流（后端中间件）。

---

## 3. 接口与契约（API Contracts）

统一前缀：/api（需有效JWT）

- POST /api/sessions  
  - req: { text: string }  
  - res: { id: string, title: string, created_at: string }
- GET /api/sessions  
  - res: { items: Array<{ id: string, title: string, created_at: string, preview?: string }> }
- GET /api/sessions/:id  
  - res: { id, title, created_at, messages: Message[], summary?: Summary, reading_position?: number }
- POST /api/sessions/:id/messages  
  - req: { content: string }  
  - res: 流式 text/event-stream 或最终 { message: Message, summary?: Summary }

统一错误模型（参考附录 12.1）  
- 响应头含 trace_id；body.error.trace_id 一致。  
- 前端将错误以可理解文案显示，并提供复制诊断信息与重试。

---

## 4. 数据视图（Data View）

4.1 ERD  
```mermaid
erDiagram
    USERS ||--o{ SESSIONS : has
    SESSIONS ||--|{ MESSAGES : contains
    SESSIONS ||--|{ SUMMARIES : has
    SESSIONS ||--o{ CHUNKS : may_have
    SESSIONS ||--o{ PROGRESS : tracks

    USERS { UUID id, string email }
    SESSIONS { UUID id, UUID user_id, string title, int active_chunk_index, jsonb reading_state, datetime created_at }
    MESSAGES { UUID id, UUID session_id, string role, string content, datetime created_at }
    SUMMARIES { UUID id, UUID session_id, int version, string text, jsonb structured_content, datetime created_at }
    CHUNKS { UUID id, UUID session_id, int index, string heading, string preview }
    PROGRESS { UUID task_id, UUID session_id, string status, int progress }
```

4.2 设计要点  
- RLS：按 user_id 限制访问（Supabase RLS 策略）。  
- Summaries 采用追加式版本（version 自增），便于回溯与比较。  
- 读取性能：对 sessions(user_id, created_at)、messages(session_id, created_at) 建索引。  
- 迁移：Alembic 管理 schema 版本与回滚。

---

## 5. 进程/部署视图（Process & Deployment）

5.1 运行时拓扑  
```mermaid
graph LR
    subgraph Browser
      SPA[React SPA]
    end
    subgraph Cloud
      API[FastAPI App (Uvicorn/Gunicorn)]
      DB[(PostgreSQL)]
      ST[Object Storage]
      SENTRY[Sentry]
    end
    SPA -->|HTTPS| API
    API --> DB
    API --> ST
    API -->|HTTPS| LLM[(LLM Providers)]
    API --> SENTRY
```

5.2 部署平台（MVP）  
- 前端：Vercel/Netlify（主干合并自动生产部署，develop 分支预览）。  
- 后端：Render/Fly.io（容器化，滚动更新；环境变量管理）。  
- 数据层：Supabase（PG/Auth/Storage）。

5.3 CI/CD（GitHub Actions）  
- On PR/Push develop：Lint/Format、单测（FE/BE）、构建（FE/Docker）、部署预览。  
- On Merge main：上一步 + 集成测试 + Alembic 迁移 + 生产部署。  
- Secrets：使用 GitHub Encrypted Secrets。

---

## 6. 技术栈与项目结构（Tech & Project Structure）

6.1 技术栈  
- FE：React 18 + Vite 5，状态（Zustand/Redux），Vitest + RTL。  
- BE：FastAPI 0.110.x，Uvicorn/Gunicorn，Pytest。  
- DB：PostgreSQL 15（Supabase）。  
- 观测：Sentry。  
- 部署：Vercel/Netlify + Render/Fly.io。  
- LLM：Adapter 模式（OpenAI/Claude/Gemini 可插拔）。

6.2 Monorepo 结构（参考）  
```
/phoenix-learning-app
├── apps/
│   ├── backend/
│   │   ├── app/
│   │   │   ├── api/        # 路由/端点
│   │   │   ├── core/       # 配置/中间件/错误与追踪
│   │   │   ├── crud/       # Repository 层
│   │   │   ├── models/     # Pydantic Schema
│   │   │   ├── services/   # 业务编排/LLM 调用
│   │   │   └── main.py
│   │   ├── tests/
│   │   └── requirements.txt
│   └── frontend/
│       ├── src/
│       │   ├── api/
│       │   ├── components/
│       │   ├── pages/
│       │   ├── store/
│       │   └── App.tsx
│       └── package.json
├── packages/
│   └── shared-types/
└── docs/
```

---

## 7. 横切关注点（Cross-Cutting Concerns）

7.1 安全（Security）  
- 鉴权：Supabase Auth JWT；后端中间件统一校验与注入用户上下文。  
- 授权：RLS + 业务侧二次校验（session.user_id == current_user）。  
- 输入校验：Pydantic 模型严格校验；限制内容大小与速率。  
- 传输与静态加密：HTTPS/TLS，全托管静态加密（Supabase）。

7.2 可观测性（Observability）  
- 日志：JSON，包含 timestamp、level、trace_id、path、user_id（如有）、duration_ms、error.code。  
- 指标：API 请求率/错误率/延迟（P50/P90/P99）、LLM 调用/Token/延迟/错误率。  
- 告警：API 错误率、P99 延迟、LLM 成本阈值 → Slack 通知。  
- 追踪：trace_id 贯穿 FE/BE/错误上报。

7.3 成本与性能（Cost & Performance）  
- LLM：流式响应（降低TTFB）、请求合并、提示模板复用、结果缓存（非个性化）。  
- API：客户端指数退避重试；服务端合理超时与有限重试；速率限制。  
- DB：热点索引与分页查询；必要时只读副本（后期）。  
- 平台：预算告警与配额；按需横向扩展。

---

## 8. 运行手册（Runbook）

8.1 环境变量（.env.example 节选）  
- SUPABASE_URL / SUPABASE_ANON_KEY / SUPABASE_SERVICE_ROLE_KEY  
- DATABASE_URL  
- LLM_API_KEY / LLM_API_BASE  
- SENTRY_DSN

8.2 本地启动  
- Backend  
  ```bash
  cd apps/backend
  pip install -r requirements.txt
  uvicorn app.main:app --reload
  ```  
- Frontend  
  ```bash
  cd apps/frontend
  npm install
  npm run dev
  ```

8.3 常见故障  
- 401/403：JWT 过期或无效→前端刷新/重登，后端校验日志检查。  
- 5xx：LLM 超时/错误→查看服务端超时与重试日志，检查配额与网络；触发告警。  
- 延迟升高：排查 P99 指标→检查LLM延迟、数据库慢查询、平台限流。  
- 成本异常：预算告警→临时降级模型/限流，评估请求合并与缓存策略。

---

## 9. 演进路线（Evolution Roadmap）

阶段A（MVP，0-6周）  
- 完成 FR1–FR9 端到端闭环；观测/错误模型/重试与速率限制基线；Sentry 接入。  

阶段B（6-12周）  
- 多格式导入（URL/PDF）与预处理；摘要↔对话↔原文三向追溯闭环；卡片/间隔重复试验版。  

阶段C（12周+）  
- 知识网络化/跨文档连接；语音学习模式；成本/可靠性持续优化与A/B 实验体系化。

---

## 12.4 前端微架构：客户端 SSE 解析与中止链路

本节对 1.10/1.11/1.12 中前端 apiClient 的流式解析、错误分类、超时与中止联动进行微架构归档，作为后续重构与测试的一致依据。

设计目标
- 统一行为：同一路径支持 text/event-stream 流式与最终 JSON 两种后端返回格式，保证 UI 行为一致。
- 类型安全：对 SSE 事件定义受控类型，降低 any 使用并提升单测可读性。
- 稳健可测：SSE 解析提取为可测试单元，覆盖 UTF-8 多字节、坏 JSON、流中断等边界。
- 资源可控：总超时≤10s，AbortSignal 贯穿；中止时应真正 abort fetch，避免“逻辑超时但请求仍在进行”。

关键构件
- API 客户端封装：[apps/frontend/src/api/client.ts](apps/frontend/src/api/client.ts:1)
  - 统一错误分类：[`function classifyError(error)`](apps/frontend/src/api/client.ts:103)
  - 指数退避与总时限：[`function retryWithBackoff(fn, opts)`](apps/frontend/src/api/client.ts:159)
  - 总体超时包装：[`function fetchWithTimeout(fn, opts)`](apps/frontend/src/api/client.ts:132)
  - 消息发送（含流式）：[`function sendMessage()`](apps/frontend/src/api/client.ts:319)
- 待抽取的工具与解析（建议纳入 1.12 Phase 1）
  - 中止信号联动：`linkAbortSignals(controller, ...signals)` 用于替换重复的监听/清理逻辑（现有重复点：[`sendMessage()` 内部](apps/frontend/src/api/client.ts:332-339) 与 finally 清理段落 [apps/frontend/src/api/client.ts:440-452](apps/frontend/src/api/client.ts:440-452)）。
  - SSE 解析模块：`parseSSEStream(reader, onEvent)` 用于承载解码/分割/JSON 解析，替换当前解析循环（现实现位于 [apps/frontend/src/api/client.ts:367-407](apps/frontend/src/api/client.ts:367-407)）。
  - 事件类型：`interface SSEEvent<T=unknown> { event?: string; data?: T; raw?: string }`，在解析失败时以 `raw` 附带原始行，便于调试。

信号链路与生命周期
- 外部中止：调用方提供的 `AbortSignal` 可直接中止；未提供时由 `fetchWithTimeout()` 创建“总体控制器”并在到达总时限时触发 `abort()`。
- 重试过程：`retryWithBackoff()` 为每次尝试创建子控制器，且与总体控制器联动；在退避等待窗口中继续监听总体中止事件，确保 ≤10s 则停止后续尝试。
- 流式路径：`sendMessage()` 在发起 fetch 前将外部 signal 与总体 signal 与局部控制器联动；在 finally 中统一移除监听，避免事件泄漏。

错误分类与 UI 映射
- 分类规则：AbortError⇒timeout；TypeError 且无 status⇒network；401/403⇒unauthorized；404⇒notfound；5xx⇒server；其余⇒unknown。
- UI 常量：集中于 [apps/frontend/src/constants/ui.ts](apps/frontend/src/constants/ui.ts:1)，包含 `errorTexts.*`、`skeletonMinDurationMs`、`longLoadingMs`、`emptyTexts.*` 等键，供 Reader/组件统一消费。
- 追踪透传：服务端 `X-Trace-Id` 回传至 `_traceId`；错误对象 `ApiError` 携带 `traceId` 与 `responseJson`，便于诊断与测试。

重构计划与实施阶段
- Phase 1（1.12 内完成）
  - 提取 `linkAbortSignals()` 与 `parseSSEStream()`，以最小侵入替换现有重复/复杂片段；
  - 引入 `SSEEvent` 类型并改写解析回调签名；
  - 测试补齐：UTF-8 多字节跨 chunk、坏 JSON 容错、流中断、请求前/中止、退避窗口中止与 10s 总时限。
- Phase 2（1.12 后续或 1.13）
  - 5xx 选择性重试：对 502/503/504 开启重试，并根据 `Retry-After` 自适应退避；
  - 错误日志节流：按 `traceId` 或时间窗抑制重复日志，降低网络异常时的控制台噪音；
  - 背压与性能：利用 Web Streams API 背压能力，避免构建大型中间缓冲区。

参考实现与证据
- 实现位置：[`client.ts`](apps/frontend/src/api/client.ts:1) 中的 [`classifyError()`](apps/frontend/src/api/client.ts:103)、[`fetchWithTimeout()`](apps/frontend/src/api/client.ts:132)、[`retryWithBackoff()`](apps/frontend/src/api/client.ts:159)、[`sendMessage()`](apps/frontend/src/api/client.ts:319)。
- 测试证据：[`client.test.ts`](apps/frontend/src/api/client.test.ts:1) 对重试/总时限/外部中止与流式/最终模式用例的覆盖。

## 10. 风险与缓解（Risks & Mitigations）

- 动态摘要质量波动（高）：最小可解释结构上线，灰度优化提示策略；提供“一键回到原文”。  
- 成本不可控（高）：合并请求、缓存、限流、预算告警、分层模型策略（复杂问题用强模型，基础用性价比模型）。  
- 可用性门槛（中）：提供引导模板与轻量模式，降低认知负荷。  
- 供应商锁定（中）：适配器抽象，预留迁移路径至自建或国内云。

---

## 11. 架构决策记录（ADR 摘要）

- ADR-001：单体 FastAPI + 适配层 而非微服务（MVP 复杂度与团队规模权衡）。  
- ADR-002：Supabase（PG/Auth/Storage）优先（MVP 交付速度），通过适配器保持可迁移性。  
- ADR-003：流式响应 与 统一错误模型 必须实现（体验与可运维性）。  
- ADR-004：前端直连 Auth 仅用于登录/刷新，业务数据统一走后端（安全与治理）。

---

## 12. 附录（Appendix）

12.1 统一错误响应模型  
```json
{
  "error": {
    "code": "UNIQUE_ERROR_CODE",
    "message": "对用户友好的错误信息。",
    "details": {
      "field": "specific_field_if_any",
      "reason": "technical_reason_for_the_error"
    },
    "trace_id": "request_trace_uuid"
  }
}
```

12.2 前后端共享类型（节选）  
- Message: { id, role: 'user'|'assistant', content, created_at }  
- Summary: { id, session_id, version, text, structured_content?, created_at }  
- Session: { id, title, active_chunk_index?, reading_state?, created_at }

12.3 参考  
- 简报：[`docs/brief.md`](docs/brief.md)  
- PRD：[`docs/prd.md`](docs/prd.md)  
- 前端规范：[`docs/ui-ux.md`](docs/ui-ux.md)

（全栈架构文档 V2.2 完）