# 12. 附录（Appendix）

12.1 统一错误响应模型  
```json
{
  "error": {
    "code": "UNIQUE_ERROR_CODE",
    "message": "对用户友好的错误信息。",
    "details": {
      "field": "specific_field_if_any",
      "reason": "technical_reason_for_the_error"
    },
    "trace_id": "request_trace_uuid"
  }
}
```

12.2 前后端共享类型（节选）  
- Message: { id, role: 'user'|'assistant', content, created_at }  
- Summary: { id, session_id, version, text, structured_content?, created_at }  
- Session: { id, title, active_chunk_index?, reading_state?, created_at }

12.3 参考  
- 简报：[`docs/brief.md`](docs/brief.md)  
- PRD：[`docs/prd.md`](docs/prd.md)  
- 前端规范：[`docs/ui-ux.md`](docs/ui-ux.md)

（全栈架构文档 V2.2 完）