# 6. 技术栈与项目结构（Tech & Project Structure）

6.1 技术栈  
- FE：React 18 + Vite 5，状态（Zustand/Redux），Vitest + RTL。  
- BE：FastAPI 0.110.x，Uvicorn/Gunicorn，Pytest。  
- DB：PostgreSQL 15（Supabase）。  
- 观测：Sentry。  
- 部署：Vercel/Netlify + Render/Fly.io。  
- LLM：Adapter 模式（OpenAI/Claude/Gemini 可插拔）。

6.2 Monorepo 结构（参考）  
```
/phoenix-learning-app
├── apps/
│   ├── backend/
│   │   ├── app/
│   │   │   ├── api/        # 路由/端点
│   │   │   ├── core/       # 配置/中间件/错误与追踪
│   │   │   ├── crud/       # Repository 层
│   │   │   ├── models/     # Pydantic Schema
│   │   │   ├── services/   # 业务编排/LLM 调用
│   │   │   └── main.py
│   │   ├── tests/
│   │   └── requirements.txt
│   └── frontend/
│       ├── src/
│       │   ├── api/
│       │   ├── components/
│       │   ├── pages/
│       │   ├── store/
│       │   └── App.tsx
│       └── package.json
├── packages/
│   └── shared-types/
└── docs/
```

---
