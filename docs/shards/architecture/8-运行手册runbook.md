# 8. 运行手册（Runbook）

8.1 环境变量（.env.example 节选）  
- SUPABASE_URL / SUPABASE_ANON_KEY / SUPABASE_SERVICE_ROLE_KEY  
- DATABASE_URL  
- LLM_API_KEY / LLM_API_BASE  
- SENTRY_DSN

8.2 本地启动  
- Backend  
  ```bash
  cd apps/backend
  pip install -r requirements.txt
  uvicorn app.main:app --reload
  ```  
- Frontend  
  ```bash
  cd apps/frontend
  npm install
  npm run dev
  ```

8.3 常见故障  
- 401/403：JWT 过期或无效→前端刷新/重登，后端校验日志检查。  
- 5xx：LLM 超时/错误→查看服务端超时与重试日志，检查配额与网络；触发告警。  
- 延迟升高：排查 P99 指标→检查LLM延迟、数据库慢查询、平台限流。  
- 成本异常：预算告警→临时降级模型/限流，评估请求合并与缓存策略。

---
