# 2. 逻辑视图（Logical View）

2.1 分层与模块  
- Web 层（FastAPI API）：路由/序列化/鉴权门面。  
- 应用服务层（Services）：会话编排、上下文组装、摘要生成与更新、错误与追踪注入。  
- 领域/仓库层（Domain/Repository）：Sessions、Messages、Summaries 等实体与仓库接口（抽象 CRUD）。  
- 适配器层（Adapters）：DB（PG/Alembic）、Auth（Supabase JWT 校验）、Storage（对象存储）、LLM（Provider 抽象）。  
- 前端客户端（apiClient）：统一请求、错误模型、trace_id 注入、指数退避重试。

2.2 核心用例流（MVP）  
- 创建会话：粘贴文本→POST /api/sessions→持久化会话→返回ID→Library 列表可见。  
- 阅读→学习：Reader 顶部“与导师学习”→进入 Session（左对话右内容）。  
- 对话与摘要：POST /api/sessions/{id}/messages（流式）→服务层调用LLM→更新摘要→返回消息与摘要→右栏摘要提示更新→可切换原文/摘要。  
- 断点续学：GET /api/sessions/{id}→恢复消息、摘要、阅读位置与UI偏好。

2.3 横切逻辑  
- 错误与统一响应：error.code/message/details/trace_id。  
- Trace：每请求注入 trace_id，贯穿日志、错误与前端上报。  
- 重试策略：客户端指数退避（网络/5xx），服务端对外部调用设定超时与有限重试。  
- 速率限制与配额：按用户或IP节流（后端中间件）。

---
