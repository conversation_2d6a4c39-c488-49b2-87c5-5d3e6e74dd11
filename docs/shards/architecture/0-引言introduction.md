# 0. 引言（Introduction）

0.1 文档目的  
定义“知深学习导师”的全栈目标架构，作为技术真相来源（SSOT），指导MVP实现与后续演进。覆盖系统视图、逻辑与部署视图、数据视图、接口契约与横切关注点（安全、可观测、成本/性能），并给出运行手册与风险/决策记录。

0.2 范围与读者  
- 范围：前端（Vite+React SPA）、后端（FastAPI 单体）、基础设施（Supabase PG/Auth/Storage + 托管平台）、LLM适配层。  
- 读者：架构/后端/前端工程师、QA、DevOps、PM/UX。

0.3 架构动因（来自PRD/简报）  
- 核心体验：动态上下文管理与可交互摘要，支撑长程有记忆的深度对话学习。  
- UI心智模型：左对话｜右内容（原文/摘要切换），专注、极简。  
- 技术策略：MVP期以 BaaS（Supabase）提速；通过适配层保持可迁移性。  
- 非目标（MVP）：多格式导入、知识图谱、全语音/播客、协作分享等。

0.4 变更日志  
- 2025-08-06 V2.2：升级为BMaD完整目录，细化系统/逻辑/部署/数据视图，补充错误与追踪、成本与可靠性基线与运行手册，收敛与 PRD/前端规范一致。

---
