# 7. 横切关注点（Cross-Cutting Concerns）

7.1 安全（Security）  
- 鉴权：Supabase Auth JWT；后端中间件统一校验与注入用户上下文。  
- 授权：RLS + 业务侧二次校验（session.user_id == current_user）。  
- 输入校验：Pydantic 模型严格校验；限制内容大小与速率。  
- 传输与静态加密：HTTPS/TLS，全托管静态加密（Supabase）。

7.2 可观测性（Observability）  
- 日志：JSON，包含 timestamp、level、trace_id、path、user_id（如有）、duration_ms、error.code。  
- 指标：API 请求率/错误率/延迟（P50/P90/P99）、LLM 调用/Token/延迟/错误率。  
- 告警：API 错误率、P99 延迟、LLM 成本阈值 → Slack 通知。  
- 追踪：trace_id 贯穿 FE/BE/错误上报。

7.3 成本与性能（Cost & Performance）  
- LLM：流式响应（降低TTFB）、请求合并、提示模板复用、结果缓存（非个性化）。  
- API：客户端指数退避重试；服务端合理超时与有限重试；速率限制。  
- DB：热点索引与分页查询；必要时只读副本（后期）。  
- 平台：预算告警与配额；按需横向扩展。

---
