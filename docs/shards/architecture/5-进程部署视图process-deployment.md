# 5. 进程/部署视图（Process & Deployment）

5.1 运行时拓扑  
```mermaid
graph LR
    subgraph Browser
      SPA[React SPA]
    end
    subgraph Cloud
      API[FastAPI App (Uvicorn/Gunicorn)]
      DB[(PostgreSQL)]
      ST[Object Storage]
      SENTRY[Sentry]
    end
    SPA -->|HTTPS| API
    API --> DB
    API --> ST
    API -->|HTTPS| LLM[(LLM Providers)]
    API --> SENTRY
```

5.2 部署平台（MVP）  
- 前端：Vercel/Netlify（主干合并自动生产部署，develop 分支预览）。  
- 后端：Render/Fly.io（容器化，滚动更新；环境变量管理）。  
- 数据层：Supabase（PG/Auth/Storage）。

5.3 CI/CD（GitHub Actions）  
- On PR/Push develop：Lint/Format、单测（FE/BE）、构建（FE/Docker）、部署预览。  
- On Merge main：上一步 + 集成测试 + Alembic 迁移 + 生产部署。  
- Secrets：使用 GitHub Encrypted Secrets。

---
