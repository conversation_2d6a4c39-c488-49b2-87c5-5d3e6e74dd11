# 3. 接口与契约（API Contracts）

统一前缀：/api（需有效JWT）

- POST /api/sessions  
  - req: { text: string }  
  - res: { id: string, title: string, created_at: string }
- GET /api/sessions  
  - res: { items: Array<{ id: string, title: string, created_at: string, preview?: string }> }
- GET /api/sessions/:id  
  - res: { id, title, created_at, messages: Message[], summary?: Summary, reading_position?: number }
- POST /api/sessions/:id/messages  
  - req: { content: string }  
  - res: 流式 text/event-stream 或最终 { message: Message, summary?: Summary }

统一错误模型（参考附录 12.1）  
- 响应头含 trace_id；body.error.trace_id 一致。  
- 前端将错误以可理解文案显示，并提供复制诊断信息与重试。

---
