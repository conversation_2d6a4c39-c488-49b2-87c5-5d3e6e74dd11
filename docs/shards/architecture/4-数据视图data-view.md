# 4. 数据视图（Data View）

4.1 ERD  
```mermaid
erDiagram
    USERS ||--o{ SESSIONS : has
    SESSIONS ||--|{ MESSAGES : contains
    SESSIONS ||--|{ SUMMARIES : has
    SESSIONS ||--o{ CHUNKS : may_have
    SESSIONS ||--o{ PROGRESS : tracks

    USERS { UUID id, string email }
    SESSIONS { UUID id, UUID user_id, string title, int active_chunk_index, jsonb reading_state, datetime created_at }
    MESSAGES { UUID id, UUID session_id, string role, string content, datetime created_at }
    SUMMARIES { UUID id, UUID session_id, int version, string text, jsonb structured_content, datetime created_at }
    CHUNKS { UUID id, UUID session_id, int index, string heading, string preview }
    PROGRESS { UUID task_id, UUID session_id, string status, int progress }
```

4.2 设计要点  
- RLS：按 user_id 限制访问（Supabase RLS 策略）。  
- Summaries 采用追加式版本（version 自增），便于回溯与比较。  
- 读取性能：对 sessions(user_id, created_at)、messages(session_id, created_at) 建索引。  
- 迁移：Alembic 管理 schema 版本与回滚。

---
