# 1. 系统视图（C4：System Context）

1.1 上下文与边界  
- 使用者：登录用户（知识工作者/学生）。  
- 外部系统：LLM Provider（OpenAI/Claude/Gemini 等，可插拔）、身份认证（Supabase Auth）、对象存储（Supabase Storage）、数据库（Supabase PG）。  
- 系统边界：前端SPA仅经后端API访问数据；认证仅前端直连Auth用于登录/刷新。

1.2 系统关系图  
```mermaid
graph TD
    U[用户] -->|浏览器| FE[React SPA]
    FE -->|HTTPS/JSON| BE[FastAPI 后端]
    FE -.->|OAuth/JWT| AUTH[Supabase Auth]
    BE --> DB[(PostgreSQL via Supabase)]
    BE --> STG[Supabase Storage]
    BE --> LLM[LLM Providers via Adapter]
```

关键约束  
- 前端禁止直连数据库/存储。  
- 认证SDK仅用于登录/刷新，业务数据均走后端。  
- 后端是唯一业务编排与数据访问入口。

---
