# 15. 附录（Appendix）

15.1 统一错误响应模型（参考）  
```json
{
  "error": {
    "code": "UNIQUE_ERROR_CODE",
    "message": "对用户友好的错误信息。",
    "details": {
      "field": "specific_field_if_any",
      "reason": "technical_reason_for_the_error"
    },
    "trace_id": "request_trace_uuid"
  }
}
```

15.2 环境变量模板（节选，详见架构文档12.1）  
- SUPABASE_URL / ANON_KEY / SERVICE_ROLE_KEY  
- DATABASE_URL  
- LLM_API_KEY / LLM_API_BASE  
- SENTRY_DSN

15.3 相关文档  
- 项目简报（完整版）：[`docs/brief.md`](docs/brief.md)  
- 全栈架构文档 V2.1：[`docs/architecture.md`](docs/architecture.md)  
- UI/UX规格说明书 V2.0：[`docs/ui-ux.md`](docs/ui-ux.md)

（PRD V2.1 完）