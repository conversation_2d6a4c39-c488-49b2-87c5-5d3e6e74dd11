# 5. 需求（Requirements）

5.1 功能性需求（FR）  
- FR1 会话创建：用户将纯文本粘贴到输入区域，点击提交后创建学习会话。  
- FR2 内容处理：系统接收并持久化文本，生成会话ID与元信息（标题、创建时间）。  
- FR3 核心界面：提供双栏布局；左栏为对话历史与输入框；右栏为内容画布。  
- FR4 对话交互：用户在左栏输入问题或指令，系统返回AI回答（流式优先）。  
- FR5 聚焦上下文：AI回答基于当前材料与摘要核心，减少离题与幻觉。  
- FR6 动态摘要：对话过程中生成或更新“动态摘要”，反映核心概念与用户理解进展。  
- FR7 视图切换：右栏可在“原文/摘要”视图之间切换，默认态可配置。  
- FR8 进度自动保存：会话、消息、摘要、阅读位置自动持久化。  
- FR9 断点续学：回到知识库后选中历史会话，恢复上次学习状态。

5.2 非功能性需求（NFR）  
- NFR1 平台兼容：现代桌面浏览器一致性（Chrome/Firefox/Safari/Edge 最新版）。  
- NFR2 性能：P50 首屏加载<2s；P50 AI响应<3s；P50 TTFB<1s（流式）。  
- NFR3 可靠性：数据持久；服务可用性目标≥99.5%。  
- NFR4 安全性：传输与静态加密；RLS策略；输入严格校验。  
- NFR5 成本效益：请求合并、流式响应、缓存策略、预算告警。

5.3 验收标准（高层）  
- 创建会话返回ID；可进入阅读视图与学习会话视图；多轮对话稳定运行；摘要自动更新；状态可恢复。  
- 错误返回统一模型；基础观测指标可见；异常有可追溯 trace_id。

提示：详细验收标准见第9章“里程碑与交付清单”和第10章“验收标准”。

---
