# 1.8 Reader 页面统一占位全面落地与测试覆盖补强（巩固 1.7，完善测试与A11y）

Status: Done

## Story
作为一名专注深度学习的前端用户，
我希望在 Reader 页面获得与全站一致的“加载骨架/空态/错误占位”体验并具备可复制的 trace_id 与稳定的重试机制，
从而在内容加载、异常或无内容时仍能保持连贯、可信与可诊断的使用体验。

## Acceptance Criteria
1) 加载骨架（Skeleton）
   - 首次进入 Reader 时显示段落骨架（3–6段，宽度不同的条形占位），最小显示时长 300ms，避免闪烁。
   - 在数据成功返回且 paragraphs.length>0 时切换为内容态；若 paragraphs.length===0 切换为空态；请求失败切换为错误态。

2) 空态（Empty）
   - 当 paragraphs.length===0 时显示标准 EmptyState（文案采用集中配置 emptyTexts.reader）。
   - 提供 CTA：返回会话列表或刷新；交互不阻塞其他全局操作。

3) 错误占位（Error）
   - 显示统一错误标题“加载失败，请重试”，次文案展示“问题追踪ID：{trace_id}”，支持一键复制。
   - 提供“重试”与“反馈”操作；重试必须显式调用与首载相同的加载函数 load()。
   - 展示错误占位时通过 console.info 输出 { page: 'reader', trace_id, path, retryable }。

4) 一致性与可配置
   - 占位组件样式与 Library 列表一致；常量集中在 constants/ui.ts（skeletonMinDurationMs=300，emptyMinViewMs=300，errorTexts 等）。
   - 状态优先级：Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady。

5) A11y 基线
   - 占位根节点 role="status" 或 aria-live="polite"（错误态 aria-live="assertive"）。
   - 复制 trace_id 给予视觉或读屏反馈提示“已复制”。

6) 测试覆盖
   - 单测覆盖：骨架最小显隐时长、空态文案与 CTA、错误态 trace_id 显示与复制、点击“重试”后成功恢复内容态。
   - 使用 fake timers 并提供 advanceAndFlush 辅助推进渲染帧；错误→重试路径需先退出错误态再进入内容态断言通过。

## Dev Notes

本故事严格基于架构与前置故事 1.7 的统一规范与实现要求抽取，禁止发明新规范或技术栈外元素。所有技术细节来源如下：

- 前端统一错误模型与追踪
  - 统一错误响应模型与 trace_id 约定：[Source: architecture.md#12.1 附录-统一错误响应模型](docs/architecture.md:282-295)
  - 前端客户端负责注入/透出 trace_id、指数退避重试：[Source: architecture.md#2.2 逻辑视图-横切逻辑](docs/architecture.md:69-74), [Source: architecture.md#2.1 前端客户端（apiClient）职责](docs/architecture.md:61-63)
  - 跨切关注中的追踪与可观测性要求（日志、trace_id、指标）：[Source: docs/shards/architecture/7-横切关注点cross-cutting-concerns.md#7.2 可观测性](docs/shards/architecture/7-横切关注点cross-cutting-concerns.md:9-14)

- 前端项目结构与放置位置
  - Monorepo 与前端 src 结构（api/components/pages/store）：[Source: docs/shards/architecture/6-技术栈与项目结构tech-project-structure.md#6.2 结构](docs/shards/architecture/6-技术栈与项目结构tech-project-结构tech-project-structure.md:11-36)
  - 现有组件与常量集中位置：
    - Placeholders 组件集合：[Source: apps/frontend/src/components/Placeholders.tsx](apps/frontend/src/components/Placeholders.tsx:1)
    - UI 常量：[Source: apps/frontend/src/constants/ui.ts](apps/frontend/src/constants/ui.ts:1)
    - Reader 页面容器：[Source: apps/frontend/src/pages/Reader.tsx](apps/frontend/src/pages/Reader.tsx:1)

- Reader 占位规范承接 1.7
  - 状态优先级、文案与 trace_id 展示、最小骨架时长、console.info 诊断输出要求：[Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md#业务规则与统一规范](docs/stories/1.7.frontend-skeleton-empty-error-states.md:26-46)
  - 接口与数据契约一致性与 trace_id 来自后端响应头与错误体：[Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md#接口与数据契约对齐](docs/stories/1.7.frontend-skeleton-empty-error-states.md:83-88)
  - Reader 侧的接入与修复清单（统一 ErrorState/EmptyState/SkeletonReader、显式 load() 重试、最小显隐）：[Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md#细节与实现建议（修复清单）-A段](docs/stories/1.7.frontend-skeleton-empty-error-states.md:221-228)

- 测试策略与时序推进
  - 测试框架与约定（Vitest + RTL）：[Source: architecture.md#6.1 技术栈](docs/architecture.md:160-167)
  - 假时钟与帧推进、重试路径断言、trace_id 归一化优先级：[Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md#新增/明确的规范要求（避免再次出现同类 Bug）](docs/stories/1.7.frontend-skeleton-empty-error-states.md:259-272)

- 安全与性能
  - 不泄露内部错误详情，仅展示 trace_id；指数退避与客户端重试策略约束：[Source: docs/shards/architecture/7-横切关注点cross-cutting-concerns.md#7.3 成本与性能](docs/shards/architecture/7-横切关注点cross-cutting-concerns.md:15-19)

如未在架构文档中找到明确条目，需显式声明：“No specific guidance found in architecture docs”。

具体到本故事需要的实现信息清单：
- Data Models：Reader 消费的 session/detail 数据结构参考架构共享类型 [Source: architecture.md#12.2 前后端共享类型](docs/architecture.md:297-301)
- API：GET /api/sessions/:id 返回详情；错误模型与 X-Trace-Id 头一致性 [Source: architecture.md#3. 接口与契约](docs/architecture.md:81-91,92-95)
- 组件规格：
  - SkeletonReader、EmptyState、ErrorState、SkeletonSessionHeader 已在组件库中存在或在 1.7 中定义目标形态：[Source: apps/frontend/src/components/Placeholders.tsx](apps/frontend/src/components/Placeholders.tsx:1); [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md#技术实现要点（前端）](docs/stories/1.7.frontend-skeleton-empty-error-states.md:109-125)
- 文件与命名：
  - 新增或调整发生在 apps/frontend/src/pages/Reader.tsx 与 constants/ui.ts 内的常量使用，遵循现有项目结构 [Source: docs/shards/architecture/6-技术栈与项目结构tech-project-structure.md](docs/shards/architecture/6-技术栈与项目结构tech-project-structure.md:11-36)

若某一分类信息在架构缺失：
- Testing Strategy 具体断言格式：No specific guidance found in architecture docs（以 1.7 的测试片段为准）
- A11y 细节 beyond 基线：No specific guidance found in architecture docs（按 1.7 最低基线执行）

## Tasks / Subtasks
- [ ] 接入统一占位组件与状态机（AC: 1,2,3,4）
  - [ ] 在 Reader 首载流程中引入 SkeletonReader 与 SkeletonSessionHeader（顶部），保持最小 300ms 显示 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:221-228]
  - [ ] 在 paragraphs.length===0 时渲染 EmptyState.reader 文案与 CTA（返回列表/刷新） [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:225-226]
  - [ ] 失败时渲染 ErrorState，透出 trace_id，onRetry 显式调用 load()，onReport 采用 mailto [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:226]
  - [ ] 错误占位渲染时 console.info 输出诊断上下文（page/path/trace_id/retryable） [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:39-41]

- [ ] 常量与一致性（AC: 4）
  - [ ] 使用 constants/ui.ts 中 skeletonMinDurationMs, emptyMinViewMs, errorTexts, emptyTexts [Source: apps/frontend/src/constants/ui.ts](apps/frontend/src/constants/ui.ts:1)
  - [ ] 确保状态优先级 Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:27-29]

- [ ] A11y 基线（AC: 5）
  - [ ] 占位根节点添加合适的 role/aria-live（错误态 assertive），按钮 aria-label [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:121-125]
  - [ ] 复制 trace_id 成功后给予视觉/读屏反馈 “已复制” [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:124-125]

- [ ] 单元测试与时序推进（AC: 6）
  - [ ] 新增/完善 Reader.test.tsx：覆盖骨架最小显隐、空态、错误态 trace_id 显示/复制、retry→load() 成功路径 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:259-272]
  - [ ] 使用 fake timers 并实现 advanceAndFlush，推进一帧与清空微/宏任务，规避竞态 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:263-265]
  - [ ] 为错误→重试路径推进至少 skeletonMinDurationMs，先断言退出错误态，再断言内容态 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:265]
  - [ ] API spy/mocks：当需验证第二次请求命中 spy 时 mockResolvedValueOnce 覆盖兜底 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:266-267]

- [ ] 追踪与错误模型对齐
  - [ ] 从错误体或响应头优先获取 trace_id；归一化优先级：body.error.trace_id ＞ err.traceId ＞ URL query ＞ 随机 uuid [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:268-271]
  - [ ] 确保与后端 X-Trace-Id 一致 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:104-107], [Source: docs/architecture.md:92-95]

- [ ] 文档与变更记录
  - [ ] 在 README/CHANGELOG 标注“Reader 统一占位与测试补强（1.8）”更新记录 [Source: docs/stories/1.7.frontend-skeleton-empty-error-states.md:170-171]

## Testing
- 测试文件位置与命名：apps/frontend/src/pages/Reader.test.tsx
- 框架：Vitest + React Testing Library [Source: docs/architecture.md:160-167]
- 关键用例：
  - 骨架最小显隐：mock 成功但延迟返回，断言骨架至少 300ms 后才消失
  - 空态：返回 paragraphs=[]，断言 EmptyState.reader 文案与 CTA 存在
  - 错误态：返回错误体含 error.trace_id 或响应头 X-Trace-Id，断言文案与复制功能；console.info 被调用且包含上下文
  - 重试：点击“重试”后调用 load()，推进计时至内容就绪，断言退出错误态并进入内容态
  - trace_id 归一化：优先取 body.error.trace_id，不存在时回退 err.traceId，再回退 URL/query，最后生成 uuid

## Change Log
| Date       | Version | Description                                  | Author |
|------------|---------|----------------------------------------------|--------|
| 2025-08-06 | 0.1     | 初稿：1.8 Reader 统一占位与测试补强（Draft） | Bob    |

## Dev Agent Record
（开发阶段由 Dev Agent 填写）

- Agent Model Used:
- Debug Log References:
- Completion Notes List:
- File List:

## QA Results
（QA 阶段由 QA Agent 填写）