# 1.7 前端加载骨架与空状态统一规范（Reader｜Library 列表｜Session 详情）

## 背景
MVP 已打通会话创建、Reader 渲染与进度持久化链路（参见 1.1–1.6）。为降低首屏认知负担与异常焦虑感、统一产品观感，需要在关键页面提供一致的“加载骨架（Skeleton）/空数据（Empty）/错误占位（Error）/空历史（No History）”规范与实现指引，覆盖 Reader、Library 列表与 Session 详情三处。

参考文档：
- PRD 总文档：[`docs/prd.md`](docs/prd.md)
- 架构文档：[`docs/architecture.md`](docs/architecture.md)
- API 合同（OpenAPI）：[`docs/openapi.yaml`](docs/openapi.yaml)
- 已完成故事：
  - 1.3 会话列表与 Reader 渲染：[`docs/stories/1.3.frontend-get-sessions-and-reader-render.md`](docs/stories/1.3.frontend-get-sessions-and-reader-render.md)
  - 1.4 阅读滚动与进度上报：[`docs/stories/1.4.frontend-reader-scroll-and-progress.md`](docs/stories/1.4.frontend-reader-scroll-and-progress.md)
  - 1.5 进度持久化：[`docs/stories/1.5.frontend-reader-progress-persist.md`](docs/stories/1.5.frontend-reader-progress-persist.md)
  - 1.6 跨设备同步与恢复（含统一错误模型与 trace_id）：[`docs/stories/1.6.cross-device-session-sync-and-resume.md`](docs/stories/1.6.cross-device-session-sync-and-resume.md)

## 目标与范围
- 目标：在 Reader、Library 列表与 Session 详情统一引入骨架/空/错误/空历史占位，提供一致的视觉/交互与错误文案基线，并与统一错误模型和 trace_id 对齐，显著提升可用性与一致性。
- 范围：
  - 页面覆盖：Reader、Library 列表（/api/sessions GET）、Session 详情（/api/sessions/{id} GET）。
  - 状态覆盖：加载骨架、空数据、错误占位、空历史（无会话）。
  - 文案基线：错误占位主文案“加载失败，请重试”，次文案显示 trace_id 可复制；操作含“重试/反馈”。
- 非范围：
  - A11y 全量规范与审计（另开后续故事）。
  - 动效系统化与 Design Token 输出（本故事提供简要建议）。

## 业务规则与统一规范
1. 状态优先级判定（按渲染时刻单一状态展示）：
   - Error ＞ Empty（或 No History）＞ Loading Skeleton ＞ Content Ready。
2. 统一错误模型接入：
   - 前端从 API 响应中提取 error.message 与 error.trace_id（详见 OpenAPI schemas.ErrorResponse）。
   - 错误占位展示主文案固定“加载失败，请重试”；次文案以“小号次文案”展示“问题追踪ID：{trace_id}”，可点击复制。
3. 交互规范：
   - 错误占位操作：主要按钮“重试”，次要按钮“反馈”（打开 mailto 或 issue 链接，包含 trace_id）。
   - 空数据/空历史：提供正向 CTA（如“去创建第一条学习会话”），引导用户继续操作。
4. 加载骨架规范：
   - Reader：骨架块模拟段落结构（3–6 块，含不同宽度的条形占位）。
   - Library 列表：骨架卡片 6–10 条，含标题/副标题条形。
   - Session 详情：标题/时间/概要骨架若干条。
5. 可观测性与诊断：
   - 在显示错误占位时，console.info 输出 { page, trace_id, path, retryable }，便于手动排查（与 1.6 追踪一致）。
6. 可配置阈值（建议 constants.ts）：
   - skeletonMinDurationMs = 300（避免骨架闪烁）
   - emptyMinViewMs = 300（避免快速切换的不适）
7. 无阻断体验：
   - 占位组件不阻塞全局交互（除必要按钮外不劫持焦点），并遵循轻量动画/渐隐。

## 页面分解与状态表述

### 1) Library 列表（GET /api/sessions）
- Loading：列表区域显示 6–10 条骨架卡片；若 200ms 内完成仍保持最小 300ms 的骨架显隐。
- Empty（200 OK 且 items.length=0）：
  - 主文案：还没有任何学习会话
  - 次文案：从一个文本开始，创建你的第一条学习会话
  - CTA：去创建第一条学习会话（点击进入创建入口）
- Error（4xx/5xx 或网络错误）：
  - 主文案：加载失败，请重试
  - 次文案：问题追踪ID：{trace_id}
  - 操作：重试（重新触发 GET）；反馈（带 trace_id）
- Content：按时间倒序渲染卡片。

### 2) Session 详情（GET /api/sessions/{id}）
- Loading：标题/时间/概要骨架。
- Empty（极端情况：会话详情存在但 messages 为空且 content.paragraphs 空）：
  - 主文案：暂无内容
  - 次文案：请返回并选择其他会话，或稍后重试
- Error：
  - 主文案：加载失败，请重试
  - 次文案：问题追踪ID：{trace_id}
  - 操作：重试/反馈
- Content：正常渲染头部信息与 Reader 入口。

### 3) Reader（最小内容展示）
- Loading：段落骨架 3–6 段；右上角显示轻量“加载中”提示。
- Empty（content.paragraphs 为空）：
  - 主文案：没有可显示的内容
  - 次文案：请返回会话列表或刷新后重试
- Error：
  - 主文案：加载失败，请重试
  - 次文案：问题追踪ID：{trace_id}
  - 操作：重试/反馈
- Content：正常渲染段落；与 1.6 的进度恢复互不影响。

## 接口与数据契约对齐
- 列表：[`docs/openapi.yaml`](docs/openapi.yaml:55-83) Sessions GET 返回 SessionListResponse。
- 详情：[`docs/openapi.yaml`](docs/openapi.yaml:84-114,400-408) SessionDetailResponse（含 content.sample）。
- 错误：统一错误模型 [`docs/openapi.yaml`](docs/openapi.yaml:484-498)。
- 追踪：后端响应头含 X-Trace-Id；错误体含 error.trace_id；前端显示与复制 trace_id。

## 验收标准（AC）
前端验收：
1) Library 列表：
   - 加载时显示骨架，接口成功且 items.length=0 时显示空历史占位（含 CTA）。
   - 异常时显示错误占位，主文案“加载失败，请重试”，次文案展示 trace_id 可复制，含“重试/反馈”。
2) Session 详情：
   - 加载骨架 → 成功渲染或空数据/错误占位；错误占位显示 trace_id 与“重试/反馈”。
3) Reader：
   - 加载骨架 → 内容/空/错误占位；错误占位显示 trace_id 与“重试/反馈”，重试后能恢复正常。
4) 体验与一致性：
   - 三处占位组件样式与文案一致（字号/间距/图标风格）；骨架最小显示 300ms，避免闪烁。
5) 诊断与日志：
   - 错误占位出现时，console.info 输出包含 trace_id 的诊断信息。
6) 不影响 1.6 的并发与恢复流程；Reader 的错误占位与 409/428 分支不冲突。

后端验收（用于配合前端验证）：
- 对任一 GET 故意返回 5xx/网络断开，可触发错误占位并正确显示 trace_id。
- X-Trace-Id 与 body.error.trace_id 一致（参考 [`apps/backend/app/api/routes.py`](apps/backend/app/api/routes.py:1)）。

## 技术实现要点（前端）
组件与放置建议：
- api 层：为客户端错误对象统一暴露 traceId（例如在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1) 中解析响应头与错误体）。
- 占位组件：
  - SkeletonList（用于 Library）
  - SkeletonReader（用于 Reader）
  - SkeletonSessionHeader（用于 Session 详情）
  - EmptyState（title, subtitle, ctaText, onCta）
  - ErrorState（title 固定，subtitle 显示 trace_id，onRetry, onReport）
- 页面接入：
  - Library 列表页面（或容器）在加载/空/错/有数据之间切换占位组件。
  - Session 详情容器在请求生命周期内切换占位组件。
  - Reader 在内容加载阶段显示骨架；若 content.paragraphs 为空或错误则切换占位。
- 文案与常量：在 constants/ui.ts 中集中维护（skeletonMinDurationMs、emptyMinViewMs、errorTexts 等）。
- A11y（最低基线）：
  - 占位组件根节点 role="status" 或 aria-live="polite"（错误用 assertive），按钮具备 aria-label。
  - 复制 trace_id 时给予视觉/读屏反馈（如“已复制”）。

示例伪代码（接口适配略）：
```
const [state, setState] = useState<'loading'|'empty'|'error'|'ready'>('loading');
const [traceId, setTraceId] = useState<string|undefined>(undefined);

useEffect(() => {
  let cancelled = false;
  const start = Date.now();
  fetchSessions()
    .then(({ items, traceId: tid }) => {
      if (cancelled) return;
      setTraceId(tid);
      const showAtLeast = Math.max(0, 300 - (Date.now() - start));
      setTimeout(() => {
        setState(items.length === 0 ? 'empty' : 'ready');
      }, showAtLeast);
    })
    .catch((err) => {
      if (cancelled) return;
      const tid = err.traceId ?? err.headers?.['x-trace-id'];
      console.info('[ListError]', { page: 'library', trace_id: tid, err });
      setTraceId(tid);
      setState('error');
    });
  return () => { cancelled = true; };
}, []);

return (
  state === 'loading' ? <SkeletonList /> :
  state === 'empty'   ? <EmptyState ctaText="去创建第一条学习会话" onCta={goCreate} /> :
  state === 'error'   ? <ErrorState subtitle={`问题追踪ID：${traceId ?? '-'}`} onRetry={reload} onReport={() => report(traceId)} /> :
                        <ListContent />
);
```

## 风险与缓解
- 骨架闪烁/误导：设置最小显示时长；仅为“首屏加载”使用骨架，不对小范围刷新滥用。
- 错误信息安全：仅显示 trace_id，不泄露内部错误详情；详细技术信息仅在控制台或后端日志。
- 文案与多语言：本故事统一中文；多语言后续由 i18n 故事接管。
- 与 1.6 交互重叠：Reader 的错误与冲突提示需独立，不互相覆盖；通过状态机统一管理。

## 完成定义（DoD）
- 前端：三处页面接入骨架/空/错占位；统一文案与操作；trace_id 可见且可复制；最小显示时长与 A11y 基线实现；console.info 诊断输出。
- 合规：不显示内部技术细节；遵循统一错误模型。
- 文档：本故事文档落库；必要时在 README/CHANGELOG 标注“统一占位规范”更新记录。

## 验证步骤（手工验收脚本）
1. Library 列表：
   - 断网或模拟 5xx，确认错误占位出现，显示主文案与 trace_id，点击“重试”后恢复正常。
   - 数据为空（后端返回 items: []），显示空历史占位与 CTA，点击进入创建流程。
2. Session 详情：
   - 人为延迟响应，观察骨架至少显示 300ms；异常返回触发错误占位并可重试。
3. Reader：
   - 首次进入显示段落骨架；当 content.paragraphs 为空时显示空状态；异常时显示错误占位，trace_id 可复制。
4. 诊断：
   - 错误占位出现时控制台输出包含 trace_id 的诊断信息。

## 依赖与影响
- 依赖：统一错误模型与 X-Trace-Id 头（已在 OpenAPI/后端实现）；前端 API 客户端需暴露 traceId（见 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1)）。
- 影响：前端需要新增占位组件与常量文件；页面容器添加状态机逻辑；对单测需补充状态切换与 trace_id 显示/复制测试。

## 变更清单（与其他文档/代码的对齐）
- OpenAPI：不需要新增契约，仅复用错误模型与现有 GET 接口。
- 前端：
  - 新增组件：SkeletonList、SkeletonReader、SkeletonSessionHeader、EmptyState、ErrorState。
  - 调整：Library 列表容器、Session 详情容器、Reader 页面接入占位状态机。
  - 常量：constants/ui.ts 新增 skeletonMinDurationMs、emptyMinViewMs、errorTexts。
- 文档：在 README 或 CHANGELOG 记录“统一占位规范（1.7）”上线说明。

## QA Results

结论
- 前端已基本实现 1.7 的统一“加载骨架/空态/错误占位”规范，覆盖 Library 列表与 ErrorState 复制 trace_id、重试/反馈交互、最小骨架显示等关键点。
- Reader 页面尚未接入统一骨架/空态/错误占位组件，当前为简易“加载中…”与内联文案，未对齐故事 1.7 的视觉与交互要求。
- Session 详情页作为独立容器暂未存在（Reader 页面承担详情展示），因此 SkeletonSessionHeader 未接入。若将详情与 Reader 解耦为单独容器，需要后续落地。

对照验收标准（AC）逐条核查
1) Library 列表：
  - 加载时显示骨架：已实现。Home.loadList 中使用 skeletonMinDurationMs 控制最小展示（[`apps/frontend/src/App.tsx`](apps/frontend/src/App.tsx:1)），UI 用 SkeletonList（[`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)）。
  - 空历史占位（items.length=0）：已实现。使用 EmptyState 并配置统一文案 emptyTexts.library（[`apps/frontend/src/App.tsx`](apps/frontend/src/App.tsx:1)、[`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1)）。
  - 异常时错误占位：已实现。ErrorState 展示统一标题与 trace_id，可重试/反馈（[`apps/frontend/src/App.tsx`](apps/frontend/src/App.tsx:1)；[`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)）。诊断 console.info 已输出 page、trace_id、path、retryable（[`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)）。
2) Session 详情：
  - 当前项目未有独立“详情容器”文件；Reader 头部承载会话元信息展示。SkeletonSessionHeader 组件已存在但未使用（[`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)）。若后续引入独立详情容器，应按规范切换骨架/空/错/内容；当前状态：部分满足（组件准备好，未接入）。
3) Reader：
  - 加载骨架：未接入 SkeletonReader（当前仅文字“加载中…”）。应在首次数据加载时使用 SkeletonReader，并保持 300ms 最小显示时长以避免闪烁。
  - 空态：当 paragraphs 为空时，目前仅内联“暂无内容”文本（[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)），未使用 EmptyState.reader 标准文案，不含 CTA。
  - 错误占位：当前以简单错误文本 + 返回按钮（[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)），未使用 ErrorState，未展示 trace_id、复制、反馈、重试入口。
  - 结论：Reader 未达标，需要补齐骨架/空/错误组件接入与时序控制。
4) 体验与一致性：
  - Library 与 ErrorState 的样式、文案、行为一致；Reader 未统一。skeletonMinDurationMs=300ms 生效于 Library；Reader 未统一控制。
5) 诊断与日志：
  - ErrorState 在展示时输出 console.info，包含 page/trace_id/path/retryable（[`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)），满足规范。Library 列表错误也额外输出一次 [ListError]（[`apps/frontend/src/App.tsx`](apps/frontend/src/App.tsx:1)）。Reader 错误路径未统一 ErrorState，因此诊断缺失。
6) 与 1.6 并发与恢复流程不冲突：
  - Reader 的进度获取/保存/冲突处理逻辑存在且健壮（[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)）。但错误占位接入未完成，因此此 AC 以“潜在不冲突，但需落地统一错误占位”标注。

细节与实现建议（修复清单）
A. Reader 接入统一占位组件与时序控制
  1. 顶部引入：从 [`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1) 引入 SkeletonReader、EmptyState、ErrorState、SkeletonSessionHeader；从 [`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1) 引入 skeletonMinDurationMs、emptyTexts。
  2. 最小骨架 300ms：在首次加载 useEffect 中记录 start，完成/失败后延时切换状态，避免闪烁。
  3. 统一空态：paragraphs.length===0 时渲染 [`EmptyState()`](apps/frontend/src/components/Placeholders.tsx:73) 使用 emptyTexts.reader 文案，并提供 CTA（返回列表/刷新）。
  4. 统一错误占位：失败时使用 [`ErrorState()`](apps/frontend/src/components/Placeholders.tsx:107)，传入 trace_id，onRetry 重新拉取详情，onReport 使用默认 mailto。确保 trace_id 尽量取自 e.traceId 或错误体。
  5. SkeletonSessionHeader：数据未就绪时，头部用 [`SkeletonSessionHeader()`](apps/frontend/src/components/Placeholders.tsx:59) 与下方 [`SkeletonReader()`](apps/frontend/src/components/Placeholders.tsx:41) 组合展示。
B. Library 细节（可选）
  1. 日志去重：保留 ErrorState 的 console.info，Home.loadList 中的 [ListError] 可降级为 debug。
  2. CTA 引导：EmptyState CTA 可改为聚焦创建输入或锚点跳转，提升可发现性。
C. A11y/微交互
  - 现有 aria-live/role 满足最低基线；复制 trace_id 的 alert 可后续替换为非阻断提示。
D. 测试建议
  - 覆盖 Library 与 Reader 的骨架最小显隐、空态文案、错误态 trace_id 显示/复制、onRetry 能恢复，以及 1.6 冲突处理与错误态的共存。
E. 追踪一致性
  - trace_id 来源优先错误体/响应头，其次 URL/query，最后回落随机 uuid，确保与后端一致追踪。

状态
- 通过：Library 列表（骨架/空/错误/trace_id/重试/反馈/诊断）。
- 待完善：Reader 页面统一占位组件接入与最小骨架显示；（可选）引入/接入 SkeletonSessionHeader；测试用例补充。

---

## QA Results（仅追加评审结果）

结论（已更新）
- Library 列表已对齐 1.7 规范：加载骨架最小 300ms、空历史 EmptyState、错误 ErrorState（含 trace_id 复制、重试/反馈、console.info 诊断）。实现参考：[`App.tsx`](apps/frontend/src/App.tsx:140-218)、[`Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:21-38,107-189)、[`ui.ts`](apps/frontend/src/constants/ui.ts:1-36)。
- Reader 页面已接入统一占位（SkeletonReader/EmptyState/ErrorState），并将“重试”改为显式调用与首载相同的加载函数 [`load()`](apps/frontend/src/pages/Reader.tsx:224-330)，避免之前通过 setState+no-op 触发 useEffect 的间接方式导致停留在 loading 的竞态。
- Session 详情单独容器当前仍由 Reader 头部承载；[`SkeletonSessionHeader()`](apps/frontend/src/components/Placeholders.tsx:59) 已用于加载态展示。如后续解耦可直接复用。

对照 AC 核查（更新后）
- AC1 Library：通过（骨架/空/错/trace_id/重试/反馈/诊断全部实现）。
- AC2 Session 详情：组件具备，Reader 头部承载信息；标记“部分满足（待独立容器接入）”。
- AC3 Reader：通过（Skeleton 最小 300ms、EmptyState.reader、ErrorState 含 trace_id、onRetry→load() 显式加载，测试覆盖通过）。
- AC4 体验一致性：通过（三处占位样式与文案统一，常量集中于 [`ui.ts`](apps/frontend/src/constants/ui.ts:1-36)）。
- AC5 诊断日志：通过（ErrorState 渲染时 console.info 输出 page/trace_id/path/retryable，Reader 与 Library 一致）。
- AC6 与 1.6 不冲突：通过（滚动恢复与进度保存逻辑保持；错误占位与冲突提示互不覆盖，状态机正确）。

新增/明确的规范要求（避免再次出现同类 Bug）
- 重试触发原则（必须遵循）
  - 所有页面的“重试”操作必须显式调用与首载相同的加载函数 load()；不得使用 setState 或 setData(no-op) 试图间接依赖 useEffect 触发。
  - 加载函数需统一处理：进入 loading、拉取数据、最小骨架时长、按结果切换 empty/ready 或 error。参考：[`load()`](apps/frontend/src/pages/Reader.tsx:224-330)。
- 计时器与渲染推进（测试约定）
  - 使用 fake timers 的 shouldAdvanceTime，并将 rAF 映射为 setTimeout(16ms)；通过 advanceAndFlush(ms) 在推进后额外推进一帧与冲掉微/宏任务，确保渲染链完整（[`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:16-29,66-76)）。
  - 出错后重试路径在点击“重试”后至少推进 skeletonMinDurationMs，并先断言退出错误态，再断言内容态（[`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:289-305)）。
- fetch 兜底与 API spy 协作
  - 测试中的 URL 定向 fetch 兜底仅在未被 api 层 spy 覆盖时生效；当需验证“第二次请求命中 spy”时，需追加 mockResolvedValueOnce，避免被兜底吞没（[`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:281-288)）。
- 错误 trace_id 归一化
  - 优先级：error.body.error.trace_id ＞ error.traceId ＞ URL query 参数 ＞ 随机 uuid；UI 始终以“问题追踪ID：{trace_id}”展示（[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:309-323), [`ui.ts`](apps/frontend/src/constants/ui.ts:5-12)）。
- 状态机与可观测性
  - 状态优先级：Error ＞ Empty ＞ LoadingSkeleton ＞ ContentReady；ErrorState 渲染时必须 console.info 输出诊断上下文（page/trace_id/path/retryable）。

状态（更新后）
- Library：通过。
- Reader：通过（显式 load() 重试、防闪烁骨架、空/错占位一致、单测覆盖通过）。
- Session 详情：保持现状；若未来拆分，可直接复用上述规范。