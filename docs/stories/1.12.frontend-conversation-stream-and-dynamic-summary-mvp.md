# 1.12 前端对话流式与动态摘要最小可用版本（FR4/FR5/FR6 收口）

状态：Approve  
类型：用户故事（前端主导，后端契约不变）  
承接：1.10 前端真实数据状态收敛；1.11 Reader 超时与可中止请求  
来源：[`docs/prd.md`](docs/prd.md:71-93,160-173)、[`docs/architecture.md`](docs/architecture.md:63-74,81-95,197-215,282-300)、[`docs/shards/architecture/3-接口与契约api-contracts.md`](docs/shards/architecture/3-接口与契约api-contracts.md:1-20)

## 一、Story

作为学习会话用户，当我在左栏输入问题时，我希望看到 AI 回答以流式方式逐步出现，并且右栏的“动态摘要”在对话过程中自动更新与提示，以便我能持续聚焦于当前材料的关键信息并快速迭代理解。

## 二、Acceptance Criteria

1. 流式回答显示  
   - 通过 POST /api/sessions/:id/messages 以流式 text/event-stream 或最终响应模式接收消息；在流式模式下，Reader/Session 视图逐字或逐块追加显示回答。  
   - 若后端返回最终模式，前端以整体消息一次性渲染，行为与流式完成态一致。  
   - 参考契约：[`docs/architecture.md`](docs/architecture.md:88-95)、[`docs/shards/architecture/3-接口与契约api-contracts.md`](docs/shards/architecture/3-接口与契约api-contracts.md:12-14)

2. 上下文聚焦与错误模型一致  
   - 所有请求通过统一 apiClient；错误分类与 trace_id 映射遵循统一规则（network/timeout/unauthorized/notfound/server/unknown）。  
   - 与 1.11 的超时/可中止策略一致，用户在等待中可取消，请求被实际中止并呈现可重试 UI。  
   - 参考：[`docs/architecture.md`](docs/architecture.md:69-74,92-95,205-215)

3. 动态摘要最小可用  
   - 每轮对话响应后，如返回 summary 字段，则更新右栏“摘要”数据与提示（例如轻量“摘要已更新”标识），不要求复杂交互，仅需可查看最新文本。  
   - 摘要采用版本覆盖或简单替换策略，后续版本化在后续故事推进。  
   - 参考数据模型与约定：[`docs/architecture.md`](docs/architecture.md:111-121,297-300)

4. UI 与交互一致性  
   - 左栏输入状态：发送中显示“生成中”，禁用发送按钮；收到响应后恢复。  
   - 右栏 Tab 原文/摘要切换不变；摘要更新时在摘要 Tab 内有轻量提示。  
   - 长加载与超时提示复用 1.11 约定与文案常量；可访问性 aria-live 用于进度与错误提示。

5. 测试与可观测  
   - 单元测试覆盖：流式增量渲染、最终响应渲染、错误分类、超时中止、摘要更新触发。  
   - 事件或日志包含 trace_id（如响应可获取），前端错误路径携带 trace 信息便于诊断。  
   - 指数退避与总超时在消息发送路径沿用 apiClient 既有实现，最大总时限≤10s。

## 三、范围内与范围外

范围内：  
- 前端 Reader/Session 视图接入流式/最终响应渲染；apiClient 增补对消息发送的封装；摘要数据的最小可用显示与更新提示；相关单测。

范围外：  
- 后端契约修改；摘要复杂结构化/版本对比 UI；完整 e2e 测试；多端同步策略变更。

## 四、Dev Notes

本节仅引用架构与 PRD已定义内容，避免臆造。

1) API 契约与流程  
- Endpoints（统一前缀 /api，JWT）：  
  - POST /api/sessions/:id/messages（流式 text/event-stream 或最终 JSON）  
  - GET /api/sessions/:id（恢复 messages 与 summary 等）  
  参考：[`docs/architecture.md`](docs/architecture.md:81-91)、[`docs/shards/architecture/3-接口与契约api-contracts.md`](docs/shards/architecture/3-接口与契约api-contracts.md:10-14)

- 错误与追踪  
  - 统一错误模型，响应头与 body.error.trace_id 一致；前端显示可理解文案并支持复制诊断与重试。  
  参考：[`docs/architecture.md`](docs/architecture.md:92-95,205-215,282-295)

- 客户端策略  
  - 指数退避重试（网络/5xx）、服务端合理超时与有限重试、前端总超时≤10s，AbortSignal 贯穿。  
  参考：[`docs/architecture.md`](docs/architecture.md:69-74,211-215)

2) 数据与模型  
- Session: { id, title, … }  
- Message: { id, role: 'user'|'assistant', content, created_at }  
- Summary: { id, session_id, version, text, structured_content?, created_at }  
参考：[`docs/architecture.md`](docs/architecture.md:298-300,111-121)

3) 前端结构与文件路径建议  
- API 封装：[`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1)  
- Reader 页面：[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)  
- 占位与错误组件：[`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1)  
- 常量：[`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1)  
- 测试：[`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:1)、[`apps/frontend/src/pages/Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:1)、[`apps/frontend/src/components/Placeholders.test.tsx`](apps/frontend/src/components/Placeholders.test.tsx:1)

4) 实现要点  
- 流式接入策略：优先使用 fetch + ReadableStream 逐块解码；若运行环境或后端返回为最终 JSON，则走一次性渲染分支，保证两种模式下 UI 行为一致。[`docs/architecture.md`](docs/architecture.md:88-95)  
- 超时/中止：延用 1.11 的总超时与 AbortSignal 贯穿；用户取消应真正 abort fetch 并归类 timeout。  
- 摘要更新：当响应附带 summary 时，替换当前摘要并显示轻量提示；避免复杂版本对比逻辑，保持 MVP 简单性。  
- 追踪：将响应头 trace_id 注入错误与日志路径，便于关联后台日志。

### Testing

测试标准与要求（依据架构与前端栈）：  
- 测试框架：Vitest + React Testing Library；测试文件与实现同层级 tests 约定已在仓库示例体现。[`docs/architecture.md`](docs/architecture.md:160-166)  
- 关键用例：  
  1) 流式增量渲染：模拟分块事件，断言文本逐步出现与最终完成态。  
  2) 最终响应渲染：一次性 JSON 返回，断言渲染等效完成态。  
  3) 错误分类：网络错误、401/404/5xx、Abort/timeout 分类与 UI 映射。  
  4) 超时/中止：总超时≤10s 触发中止，UI 显示超时并可重试。  
  5) 摘要更新：含 summary 的响应触发替换与提示。  
- 可访问性：对长加载与错误信息使用 aria-live="polite"；按钮状态有 ARIA 反馈。  
- 性能边界：流式渲染不应阻塞主线程；避免每字符重排，采用逐块或节流追加。

## 五、Tasks / Subtasks

- [ ] T1 API 客户端：发送消息封装（AC1, AC2）  
  - [ ] 在 [`client.ts`](apps/frontend/src/api/client.ts:1) 增加 sendMessage(sessionId, content, opts) 支持：  
        - 通过 AbortSignal 贯穿、指数退避、总超时≤10s（沿用 1.11 实现）  
        - 支持流式解析与最终 JSON 两种模式，返回统一迭代器或回调  
        - 错误统一通过 classifyError 归类并附带 traceId（若可获取）  
  - [ ] 单元测试：网络/5xx 重试、超时中止、流式/最终两模式

- [ ] T2 Reader 页面接入（AC1, AC4）  
  - [ ] 在 [`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1) 集成 sendMessage：  
        - 发送中禁用输入；逐块渲染；完成后恢复  
        - onCancel 调用 AbortController.abort()；错误态复用既有 ErrorState  
  - [ ] 长加载/超时文案与 aria-live 一致；重试逻辑沿用 1.11 模式  
  - [ ] 单元测试：流式/最终路径、取消与重试、长加载提示

- [ ] T3 摘要更新与提示（AC3, AC4）  
  - [ ] 当响应包含 summary：更新本地摘要状态并显示“摘要已更新”提示（轻量）  
  - [ ] 在摘要 Tab 内确保最新内容显示；不新增复杂结构  
  - [ ] 单元测试：摘要更新触发与渲染断言

- [ ] T4 常量与文案（AC2, AC4）  
  - [ ] 在 [`constants/ui.ts`](apps/frontend/src/constants/ui.ts:1) 增补：  
        - streaming.loading 文案键；summary.updated 提示键  
        - 复用 timeout 与 longLoading 键  
  - [ ] 组件引用对齐，不破坏 1.7/1.8/1.10 的断言

- [ ] T5 观测与追踪（AC5）  
  - [ ] 在关键路径打印/上报含 trace_id 的调试日志（开发环境）  
  - [ ] 确认错误路径携带 trace 信息，便于 QA 复现与排查  
  - [ ] 单元测试：traceId 映射存在时被透传

- [ ] T6 QA 与回归  
  - [ ] 覆盖 Reader 完整路径：发送→流式显示/最终显示→摘要更新→错误→重试  
  - [ ] 确认与 1.7/1.8/1.10/1.11 既有断言不冲突

## 六、Project Structure Notes

- 本故事遵循现有前端结构与文件布局，集中在 api/client、pages/Reader、components/Placeholders 与 constants/ui 路径，符合架构文档的 SPA + 统一 API 客户端模式。[`docs/architecture.md`](docs/architecture.md:61-66,158-168)  
- 未发现与既定项目结构冲突；若未来将对话从 Reader 抽象为独立 Session 视图组件，可在后续故事中演进。

## 七、Change Log

| Date       | Version | Description                              | Author |
| ---------- | ------- | ---------------------------------------- | ------ |
| 2025-08-06 | 0.1     | 初始 Draft（基于 PRD/架构与 1.10/1.11） | Bob    |

## QA Results

状态：Ready for QA（开发实现已完成并初检通过，进入故事文件正式记录）

一、实现对照验收标准核查（基于当前代码）
- AC1 流式/最终响应渲染
  - Reader 通过 [`sendMessage()`](apps/frontend/src/api/client.ts:412-519) 的 onDelta 实现逐块渲染；当后端返回最终 JSON 时以一次性 delta 回放，行为一致。
  - SSE 解析由 [`parseSSEStream()`](apps/frontend/src/api/sse.ts:59-162) 提供，支持 UTF-8 跨 chunk、事件分割与 data: 聚合。
- AC2 错误分类与中止策略一致
  - 错误分类使用 [`classifyError()`](apps/frontend/src/api/client.ts:111-154)；前端总时限通过 [`fetchWithTimeout()`](apps/frontend/src/api/client.ts:156-203) 与 [`retryWithBackoff()`](apps/frontend/src/api/client.ts:206-343) 保障；取消发送调用 Reader 的 `AbortController.abort()`。
- AC3 动态摘要最小可用
  - 当 sendMessage 返回 summaryText 时，Reader 设置轻量提示并 2s 后自动隐藏；同时更新右栏 `summary_latest` 文本（避免对 never 的强行断言，采用 any 过渡）。实现位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:421-448,556-583)。
- AC4 UI 与交互一致性
  - 发送中禁用输入、按钮显示“生成中”，支持取消。长加载提示具备 aria-live 并增加精准选择器 data-testid。实现位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:495-509,535-553)。
- AC5 测试与可观测
  - 引入 data-testid="loading-long-hint"；traceId 由 API 层 `_traceId` 透传，Reader 捕获并记录于诊断区块。实现位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:500-507,784-803)、[`client.ts`](apps/frontend/src/api/client.ts:353-379)。

二、关键改动与质量评估
1) 长加载提示
   - 已在加载态超过阈值时显示，节点包含 data-testid 与 aria-live。位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:495-509)。
2) 摘要更新提示（短暂显示）
   - 设置后 2 秒隐藏；提示位于摘要区块顶部，符合轻提示要求。位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:556-563)。
3) 发送失败自动重试与手动重试
   - 状态 sendError/sendAttempt/sendAutoRetried 唯一定义；抽取 doSend()；按 [`classifyError()`](apps/frontend/src/api/client.ts:111-154) 仅对 network/timeout/server-retryable 自动重试一次并等待 2s；失败后显示错误与“重试”按钮。位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:392-399,411-475,622-637)。
4) TS 类型修复
   - 避免直接在可能为 never 的对象上取 version；使用 prevSummary:any + nextVersion 计算，修复类型报错。位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:430-447)。
5) 重复声明清理
   - sendError/sendAttempt/sendAutoRetried 与 doSend 均为唯一定义，移除重复声明导致的块级变量冲突风险。核查位置：[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:392-399,411-475)。

三、API 层实现健壮性复核
- `linkAbortSignals()` 与 `parseSSEStream()` 已在 [`sse.ts`](apps/frontend/src/api/sse.ts:1-50,59-162) 抽取，客户端在 [`sendMessage()`](apps/frontend/src/api/client.ts:422-518) 中调用，finally 中进行了清理，避免监听泄漏。
- `retryWithBackoff()` 支持外部 signal 联动与总时限；对 502/503/504 作为 retryable，符合故事阶段目标。实现：[`client.ts`](apps/frontend/src/api/client.ts:206-343)。

四、测试现状与补充建议
- 当前 Reader 单测包含：
  - 长加载提示可见性（基于 data-testid）；自动重试失败后错误展示与手动重试成功路径；摘要更新提示出现与 2s 后消失。位置：[`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:25-111)。
- 建议新增用例（作为后续 PR 纳入 T6）：
  1) classifyError 行为分支覆盖：401/404/5xx/Abort/TypeError(network) 映射验证。参考：[`client.ts`](apps/frontend/src/api/client.ts:111-154)。
  2) sendMessage SSE 解析边界：坏 JSON 以 evt.raw 呈现不致崩溃；UTF-8 多字节跨 chunk 正确拼接。参考：[`sse.ts`](apps/frontend/src/api/sse.ts:59-162)。
  3) Reader 取消流程：isSending 时点击“取消生成”触发 abort，UI 恢复无残留。
  4) 总时限≤10s 约束：退避窗口+请求时间之和触发外层超时，归类 timeout。

五、风险与回归点
- 交互节流与计时器：Reader 使用 setTimeout 控制长加载与提示隐藏，需要在测试中启用 fake timers 并推进时间，避免脆弱断言。示例已覆盖：[`Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:37-45,101-107)。
- 诊断区块输出含动态字段（时间戳、ETag），避免在快照测试中直接比对全量 JSON，以选择性断言替代。
- API 基于 Headers 的 X-Trace-Id 注入可能与后端实际 casing 有差异，已通过 [`handleResponse()`](apps/frontend/src/api/client.ts:353-379) 兜底提取，无阻断风险。

结论
- 现实现与 1.12 phase2 的验收标准对齐，Reader 端完成流式/最终响应一致渲染、错误分类与一次自动重试、动态摘要最小可用与提示、长加载可访问性提示，并具备可观测性与必要测试覆盖。
- 状态更新为 Ready for QA。建议在后续 PR 中补齐所列测试增强项，完善 classifyError 与 SSE 边界的回归覆盖。
