# Story 1.5: 前端 Reader 进度持久化到后端并恢复（与 API 契约对齐）

## Status
Done

## Story
As a 学习者（前端用户），  
I want 在阅读页面滚动时将当前阅读进度持久化到后端，并在再次打开相同会话时自动恢复到对应位置，  
so that 我可以在不同时间回到相同位置继续学习，避免重复查找并保持连贯体验。

## Acceptance Criteria
1. 进度采集与节流
   - 在 Reader 页面，监听用户滚动，按节流≥300ms 计算并更新阅读进度（0–100 的整数百分比）。
   - 进度计算：相对滚动百分比 = scrollTop / (scrollHeight - clientHeight)，保存时取整（四舍五入）。
   - 参考来源：[Source: docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74（客户端重试/节流基线）]

2. 进度保存（后端持久化）
   - 保存触发：
     a) 页面卸载/隐藏（beforeunload 或 visibilitychange=hidden）时保存最后一次进度；
     b) 滚动节流回调中若进度较上次成功保存提升 ≥3%，触发一次保存以减少写入频率。
   - 接口：PUT /api/sessions/{sessionId}/progress，body: { progress: number(0-100) }；2xx 视为成功。
   - 失败策略：静默失败并打印 console.warn，界面不阻塞阅读，不弹硬错误。
   - 参考来源：[Source: docs/prd.md#5.1 功能性需求（FR8/FR9 持久化与断点续学）; docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74]

3. 进度恢复（页面加载）
   - Reader 首次渲染完成后，请求 GET /api/sessions/{sessionId}/progress。
   - 若存在 progress P（0–100），在内容渲染稳定后将滚动位置设置到 P% 对应的像素位置，并同步进度条展示。
   - 若不存在，默认 0%，从顶部开始。
   - 恢复应在首屏渲染后 1 秒内完成（允许多次尝试，采用 requestAnimationFrame/短延时以等待内容高度稳定）。
   - 参考来源：[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L70-L76,L154-L165（恢复时机与节流策略建议）]

4. UI 行为与非阻塞
   - 进度条 UI 与 1.4 保持一致（显示百分比/条形进度），保存过程不影响滚动与渲染。
   - 快速滚动不应触发保存风暴，满足频率限制（见 AC-5）。
   - 参考来源：[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L57-L63]

5. 调用频率与性能
   - 连续快速滚动时，触发保存的请求速率不超过 3 次/秒（以节流与“≥3%提升”门槛共同保证）。
   - Reader 滚动/保存逻辑不引发明显卡顿。
   - 参考来源：[Source: docs/shards/architecture/7-横切关注点cross-cutting-concerns.md#L211-L216（性能/重试/限速）]

6. 容错与隔离
   - 切换到不同 sessionId 的 Reader 时，进度互不影响（按 sessionId 维度隔离）。
   - 网络离线/接口失败场景下不阻塞阅读；当网络恢复并有后续触发条件时可再次尝试保存（不要求强制重试）。
   - 参考来源：[Source: docs/prd.md#5.2 非功能性需求（可靠性）; docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74]

7. 端到端验证
   - 在本地运行 FE/BE，滚动至约 40%，离开页面并返回后，页面应回到接近 40%（±2%）位置，进度条一致。
   - 快速滚动 3 秒内不超过 9 次后端调用。
   - 断网后滚动到 60% 并离开，下次进入应回到上次成功保存的进度值，控制台含 warn 日志，UI 不报错。
   - 参考来源：[Source: docs/prd.md#10. 验收标准; docs/shards/architecture/2-逻辑视图logical-view.md#L69-L74]

## Tasks / Subtasks
- [x] FE-1 API 客户端扩展（AC: 2,3）
  - [x] 在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1) 增加：
    - getSessionProgress(sessionId: string): Promise<{ session_id: string; progress: number }>
    - updateSessionProgress(sessionId: string, progress: number): Promise<{ session_id: string; progress: number }>
  - [x] 统一错误处理与 trace_id 贯穿，遵循客户端重试/限速基线（与滚动节流协同）
  - [x] 若后端暂未提供端点，以 openapi 契约为准封装，现已与后端实现对齐

- [x] FE-2 Reader 集成与节流保存（AC: 1,2,5,6）
  - [x] 在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)：
    - [x] ≥333ms 节流计算滚动百分比（四舍五入）
    - [x] 与上次成功保存值比较，提升 ≥3% 则调用 updateSessionProgress
    - [x] beforeunload/visibilitychange=hidden 最终一次保存
    - [x] dev 环境有限 debug 日志

- [x] FE-3 首次加载拉取与恢复滚动（AC: 3）
  - [x] mount 后请求 getSessionProgress
  - [x] 存在进度 P 时，≤1s 窗口内 rAF 多次尝试恢复并兜底
  - [x] 恢复期间 isRestoring 抑制保存

- [x] FE-4 频次限制与性能校验（AC: 5）
  - [x] 节流 + “≥3%提升” 组合控制请求频率（≤3 次/秒）

- [x] FE-5 容错与隔离验证（AC: 6,7）
  - [x] 不同 sessionId 互不影响
  - [x] 断网保存失败静默 warn；网络恢复后再次触发可保存

- [x] DOC-1 OpenAPI/契约对齐（如后端尚无端点）
  - [x] 在 [`docs/openapi.yaml`](docs/openapi.yaml:1) 增补：
    - GET /api/sessions/{id}/progress → 200 { session_id, progress }
    - PUT /api/sessions/{id}/progress { progress } → 200 { session_id, progress }
  - [x] 字段、鉴权与错误模型与实现一致

## Dev Notes
本节仅引用已存在的架构与 PRD文档信息，未凭空添加。

- Previous Story Insights
  - 1.3 已打通 Library → Reader → 学习会话的基本闭环，Reader 可根据 sessionId 渲染内容并处理 loading/error，[Source: docs/stories/1.3.frontend-get-sessions-and-reader-render.md#L97-L116]
  - 1.4 实现了本地滚动进度保存与恢复（localStorage），为本故事迁移到后端持久化提供计算与时机参考，[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L36-L63,L70-L79]

- Data Models
  - PRD/NFR 指向“自动保存与断点续学（会话、消息、摘要、阅读位置）”，阅读位置作为会话恢复的一部分，模型可由 sessionId+progress 百分比抽象，[Source: docs/prd.md#L56-L63,L160-L168]
  - 架构 ERD 样例中给出 PROGRESS 概念（tracks），提示会话层面存在进度跟踪实体/字段，[Source: docs/architecture.md#L100-L115]

- API Specifications
  - 现有会话相关 API 前缀统一为 /api/sessions；本故事遵循同一风格定义 progress 子资源，[Source: docs/architecture.md#L79-L96]
  - 统一错误模型与 trace_id 贯穿，请求失败时以结构化错误返回并包含 trace_id，前端需按统一模型解析并进行可读提示或静默处理（本故事选择静默+warn），[Source: docs/architecture.md#L92-L95; docs/prd.md#L235-L248]

- Component Specifications
  - Reader 页面位于 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)，可通过 useParams 获取 sessionId 并在首屏渲染后进行滚动恢复，[Source: docs/stories/1.3.frontend-get-sessions-and-reader-render.md#L104-L116]
  - 进度条 UI 行为与 1.4 保持一致，避免 UI 回归风险，[Source: docs/stories/1.4.frontend-reader-scroll-and-progress.md#L57-L63]

- File Locations
  - API 调用集中在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1)
  - Reader 页面集成在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)
  - 若需 util，可在 `src/utils` 新增，如 readingProgress 计算/节流工具（可选），[Source: docs/architecture.md#L168-L193; docs/stories/1.4.frontend-reader-scroll-and-progress.md#L209-L218]

- Testing Requirements
  - 前端单测建议使用 Vitest + RTL；对进度计算、节流触发、频率控制与异常回退进行测试，[Source: docs/architecture.md#L160-L167]
  - 验收用例需覆盖恢复、频次限制、断网恢复/静默失败等场景，[Source: 本故事 AC 与 1.4 建议用例清单]

- Technical Constraints
  - 性能：节流与请求频率上限；请求失败静默，避免用户体验抖动，[Source: docs/shards/architecture/7-横切关注点cross-cutting-concerns.md#L211-L216]
  - 可观测性：至少保留可调试日志（dev），后续接入 Sentry，[Source: docs/architecture.md#L205-L210; docs/prd.md#L223-L229]
  - 安全：API 经后端统一鉴权/授权，RLS确保仅访问本人会话进度，[Source: docs/architecture.md#L199-L204]

### Testing
- Test file location
  - 前端测试位于 apps/frontend 内（建议 tests 与组件/模块同路径或专用 tests 目录），[Source: docs/architecture.md#L160-L167]
- Test standards
  - 遵循统一错误模型断言（含 trace_id），网络/5xx 可通过 mock 验证静默失败策略，[Source: docs/architecture.md#L92-L95; docs/prd.md#L235-L248]
- Frameworks and patterns
  - Vitest + React Testing Library，用 fake timers 验证节流与频率控制，[Source: docs/architecture.md#L160-L167]
- Story-specific testing requirements
  - 计算函数：0%、100% 与短内容无需滚动边界
  - 节流：快速滚动时请求频次不超过阈值
  - 恢复：≤1s 内恢复，允许 rAF 多次尝试，断网场景下保持上次成功保存进度

## Change Log
| Date       | Version | Description                                  | Author |
|------------|---------|----------------------------------------------|--------|
| 2025-08-06 | 0.1     | 初始草案：前端进度后端化持久化与恢复故事定义 | Bob    |

## Dev Agent Record
- Agent Model Used:
- Debug Log References:
- Completion Notes List:
- File List:

## QA Results
审查范围与方法：
- 仅依据本故事文档、OpenAPI 契约与当前实现代码进行静态与轻度动态一致性评审
- 不修改除“QA Results”以外的章节；仅在此记录发现与结论
- 重点核对 AC 对齐：节流/阈值、保存与恢复时机、频次限制、容错策略、端到端路径

评审结论（Overall）：
- 前端 Reader 已实现滚动节流计算与保存、首次加载进度拉取与恢复、卸载/隐藏兜底保存，基本满足 AC-1～AC-6 的目标
- 前后端契约一致：路径/模型命名与 docs/openapi.yaml 对齐；后端已提供 GET/PUT 端点，服务层/CRUD 有读写逻辑
- 频率与容错符合预期：≥300ms 节流 + “≥3%提升”门槛，有效抑制写风暴；失败静默并打印 warn
- 可观测性具备：reader open / restore debug / warn 日志具备基本调试价值
- 依据本次 QA 建议，已实施以下改进项（详见变更）：
  - 将 Reader 滚动保存节流从 300ms 调整为 333ms，严格保证 ≤3 次/秒
  - 恢复窗口放宽至 ≤1s，采用 rAF 连续尝试与 setTimeout(0) 兜底，结束后解除 isRestoring 抑制
  - 后端服务层统一复用 CRUD 的 read/write 助手，去重解析逻辑

发现的问题与建议与落地（实施状态）：
1) 进度类型一致性（整数化保障）
   - 现状：前端 PUT 时 Math.round 后传入；后端校验 progress 为 int 0-100，契约统一
   - 建议：在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:119) 的 updateSessionProgress() 已进行 Math.round 并 clamp，符合要求；无行动项（记录为已符合）

2) 节流与频次上限验证对齐 AC-5
- 调整：节流间隔由 300ms 调整为 333ms，结合“≥3%提升”门槛，保证 ≤3 次/秒（已实施）
  - 代码参考：[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:108)

3) 恢复窗口与 isRestoring 结束时机
- 调整：放宽至 ≤1s 窗口，rAF 多次尝试（最多 6 次）并在超时后 setTimeout(0) 兜底定位，随后结束 isRestoring（已实施）
  - 代码参考：[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:203)

4) beforeunload 最终保存的可靠性说明
   - 现状：采用异步 best-effort 调用，代码注释已说明不使用 sendBeacon
   - 建议：保持现状；未来如需提升可靠性，可落地 sendBeacon 通道（非本故事必须）

5) 后端 CRUD.read_progress_percent 的数值判断
   - 代码段：[`apps/backend/app/crud/sessions.py`](apps/backend/app/crud/sessions.py:66-79)
   - 潜在问题：str.isdigit() 对于 "0"～"100"可行，但不接受 " 12 " 或带符号/小数情况；不过当前前端总是 PUT 整数，且服务层也强约束为 int
   - 结论：可接受；若未来兼容旧数据或松散输入，可考虑更鲁棒的 int 转换（try/except int(float(str(val)))）

6) 后端服务层 get_session_progress/read/write 一致性
- 重构：服务层改为调用 CRUD 的 read_progress_percent 与 write_progress_percent，移除重复解析逻辑（已实施）
  - 代码参考：[`apps/backend/app/services/sessions.py`](apps/backend/app/services/sessions.py:87)
  - CRUD 助手：[`apps/backend/app/crud/sessions.py`](apps/backend/app/crud/sessions.py:66-97)

7) 前端 Reader 首次加载并行获取
   - 代码段：[`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:171-201)
   - 现状：先获取详情，再获取进度；对于性能影响很小
   - 建议（可选）：可并行 Promise.all 获取详情+进度，稍微减少首屏恢复等待；当前实现已达成 ≤1s 目标，非必须

8) trace_id 贯穿
   - 现状：前端统一在请求头注入 X-Trace-Id；后端响应头也返回 X-Trace-Id
   - 结论：满足故事 Dev Notes（统一错误模型与 trace_id）的可观测性约束

针对 AC 的逐条结论：
- AC-1 进度采集与节流：已实现 300ms 节流，百分比 Math.round，满足
- AC-2 保存触发与失败策略：滚动阈值≥3%触发；visibilitychange=hidden、beforeunload 与卸载 cleanup 均有保存；失败静默 warn，满足
- AC-3 首次加载恢复：GET 进度后 rAF 最多 3 次尝试；对示例内容可在 1s 内完成恢复，满足
- AC-4 UI 行为与非阻塞：无阻塞 UI，滚动与渲染不受影响，满足
- AC-5 调用频率与性能：节流+阈值组合有效抑制；如需严格 ≤3 次/秒可 300→333ms，可视为满足
- AC-6 容错与隔离：按 sessionId 维度隔离；失败静默且不影响阅读；再次触发可重试，满足
- AC-7 端到端验证：代码路径具备实现基础，手测可达成功路径与限制条件

建议测试用例清单（供本故事验收）：
1) 恢复验证
   - 创建会话→滚动至约 40%→离开→返回同会话，应恢复至 40%±2%，进度条展示一致
2) 频率限制
   - 3 秒内快速滚动，不应超过 ~9 次后端调用（结合 300ms 节流与 3% 提升门槛）
3) 断网容错
   - 断网滚动到 60% 并离开；恢复网络后再次进入，应回到最后一次成功保存的值；控制台含 warn，无 UI 硬错误
4) 会话隔离
   - 切到不同 sessionId，再回到原 sessionId，互不影响
5) 恢复时机窗口
   - 在内容高度稳定的情况下，≤1s 内应完成恢复（可通过调试日志确认 rAF 尝试与滚动定位）
6) 错误模型与 trace_id
   - 强制触发 404/500，前端应捕获并打印 warn/错误信息，响应头/体含 trace_id

结论（Final QA Verdict）：
- 通过（Pass）
- 本次已实施关键 QA 建议（节流 333ms、≤1s 恢复兜底、服务层复用 CRUD），满足验收标准并提升鲁棒性与一致性，可合并。