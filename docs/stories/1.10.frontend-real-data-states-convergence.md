# 1.10 Frontend Real Data States Convergence — 统一空/错/载态与可测试性闭环

状态：In Progress
类型：用户故事（前端范围）
关联：1.7 Frontend Skeleton / Empty / Error States；1.8 Reader Unified Placeholders and Tests；1.9 Session Resume and State Persistence
来源：[`docs/prd.md`](docs/prd.md:1)、[`docs/architecture.md`](docs/architecture.md:1)、[`docs/stories/1.7.frontend-skeleton-empty-error-states.md`](docs/stories/1.7.frontend-skeleton-empty-error-states.md:1)、[`docs/stories/1.8.reader-unified-placeholders-and-tests.md`](docs/stories/1.8.reader-unified-placeholders-and-tests.md:1)

## 一、摘要

目标：在真实 API 数据与网络条件下，统一前端 Reader 与全局 UI 的加载态、空态、错误态表现与交互，确保与 1.7/1.8 的占位/测试约定保持一致，并提供稳健的降级与可测试闭环。
范围：仅前端；不修改后端契约；消费现有接口与错误语义，通过前端状态映射实现一致性。

## 二、业务背景与动机

1) 1.7/1.8 已建立占位骨架与测试框架，但主要基于模拟或理想化状态；真实 API 情况下需要统一状态管理与视觉/交互一致性。
2) 1.x 收尾阶段不宜引入后端契约变更，因此采用前端状态映射与降级策略，达成体验一致、可测试、可观测。
3) 与 1.9 的会话恢复相辅相成：恢复过程中的各种状态与失败路径需要统一 UI 与重试策略。

## 三、用户故事

作为阅读器用户，当我进入 Reader 页面或进行会话加载时，如果网络慢、数据为空或产生接口错误，我能看到与统一规范一致的加载态/空态/错误态，并带有适当的重试/引导操作；当恢复正常后，页面自动切换回内容态。

## 四、验收标准（Acceptance Criteria）

AC1 统一状态机
- 在 Reader 页面与全局首屏涉及接口处，实现统一状态机：`loading → success | empty | error`。
- 状态来源包含：创建会话、获取会话详情、获取内容段落、进度拉取/提交（仅消费已存在接口）。
- 所有状态在 UI 与测试中可观测（`data-testid` 标记齐全）。

AC2 加载态一致性
- 初次加载与懒加载均显示骨架屏或 Spinner（遵循 1.7 视觉规范）。
- 最长可见时长控制：加载超过 300ms 才显示骨架，避免闪烁；超过 10s 显示“仍在加载”弱提醒。
- 提供 `aria-busy` 与可访问性文案。

AC3 空态一致性
- 数据为空（合法响应但无内容）时，显示统一 Empty 组件，包含：
  - 说明性文案（来源 PRD tone）
  - 可行动作：刷新、返回首页、创建新会话（依据当前页上下文显示）
- 空态不应与错误态混淆，视觉 token 一致。

AC4 错误态一致性与降级
- 网络错误、超时、解析失败、后端 4xx/5xx 映射为有限错误类别：`network, timeout, unauthorized, notfound, server, unknown`。
- 每类错误对应统一文案与 CTA：
  - network/timeout：重试 + 离线提示
  - unauthorized：引导重新创建会话或回首页
  - notfound：回首页/新建
  - server/unknown：重试 + 联系支持链接（伪链接，前端提示为主）
- 错误重试具备指数退避最多 3 次（手动重试不受限）。

AC5 前端仅消费现有 API
- 不请求新增/修改后端字段。
- 错误类别映射基于现有响应码/网络异常，由前端进行归类。

AC6 可测试性闭环
- 单测覆盖：
  - 各状态渲染与切换
  - 错误映射与文案/CTA 校验
  - 重试、退避、恢复路径
- 组件/页面均提供 `data-testid` 与稳定选择器；测试不依赖视觉像素。
- 在 [`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:1) 与组件/页面测试新增覆盖，保持独立与幂等。

AC7 可访问性与国际化
- 各状态 `aria` 属性正确，焦点管理在错误/空态弹出时不丢失。
- 文案集中于常量或 i18n 位置，便于后续本地化（当前可仅中文/英文占位）。

## 五、范围内与范围外

范围内：
- 前端状态映射、统一组件与样式、Reader 页面接入、相关 API 调用点接入。
- 单元测试与基础可访问性检查。

范围外：
- 后端契约/错误码变更；OpenAPI 更新；端到端 e2e；复杂离线缓存。

## 六、设计与实现要点

1) 状态映射层
- 在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1) 的调用封装层增加错误归类函数 `classifyError(error)`：
  - 返回形如：`{ type, messageKey, original? }`，`type ∈ {network, timeout, unauthorized, notfound, server, unknown}`
  - `messageKey` 用于 UI 文案表
- 不改变现有导出签名，仅在内部统一抛出标准化错误对象（保持兼容）。

2) 统一 UI 组件
- [`apps/frontend/src/components/Placeholders.tsx`](apps/frontend/src/components/Placeholders.tsx:1) 继续承载 Skeleton、Empty、Error 三类视图，新增/强化：
  - `props`: `variant: 'loading' | 'empty' | 'error'`, `errorType?`, `onRetry?`, `primaryAction?`, `secondaryAction?`
  - `data-testid`: `placeholder-loading` / `placeholder-empty` / `placeholder-error`
  - a11y: `role`, `aria-live='polite'` 对状态文案

3) Reader 页面接入
- 在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1) 将数据调用结果映射到统一组件显示，移除分散的零散 loading/error 处理。
- 以状态机模式管理：请求 → 判空 → 渲染内容/空态/错误态；错误态绑定 `onRetry`。

4) 重试与退避
- 提供通用 `retryWithBackoff(fn, { max=3, base=500 })` 工具（可置于 `api` 或 `utils` 层）。
- 超过 N 次仍失败，保持错误态并允许手动重试。

5) 文案与常量
- 在 [`apps/frontend/src/constants/ui.ts`](apps/frontend/src/constants/ui.ts:1) 增加 Error/Empty 文案字典与 aria 文本；测试对 key 断言而非具体文案内容。

6) 渐进暴露与回归安全
- 对 1.8 中的 placeholder 测试尽量不破坏：保留核心断言；新增用例覆盖真实错误分支。
- Reader 页面优先接入；若首页/入口处同类问题，允许本故事内同步接入，确保一致性。

## 七、任务分解（面向研发）

T1 API 层：实现 `classifyError` 与 `retryWithBackoff`；保证类型与返回结构稳定。
T2 组件层：扩展 `Placeholders` 组件，支持 error/empty 细分与 CTA（重试/返回/新建）。
T3 页面层：在 `Reader.tsx` 接入统一状态机（loading/empty/error/success），移除分散处理。
T4 常量：在 `constants/ui.ts` 补齐文案映射与测试用 key。
T5 测试：
- [`apps/frontend/src/components/Placeholders.test.tsx`](apps/frontend/src/components/Placeholders.test.tsx:1)：渲染与交互（重试按钮、aria）
- [`apps/frontend/src/api/client.test.ts`](apps/frontend/src/api/client.test.ts:1)：错误归类、退避逻辑（假时钟）
- [`apps/frontend/src/pages/Reader.test.tsx`](apps/frontend/src/pages/Reader.test.tsx:1)：端内状态切换与重试路径
T6 无障碍：校验 `aria`、焦点顺序与键盘操作。

## 八、依赖与风险

依赖：现有 API 稳定；1.7/1.8 的占位与测试框架存在且可复用。
风险：真实错误多样化可能超出映射；通过 `unknown` 兜底并保持可观测日志。

## 八之附：进行中任务清单（Story 1.10）
- [x] T1 API 层：实现 classifyError 与 retryWithBackoff；保证类型与返回结构稳定
- [-] T2 组件层：扩展 Placeholders 组件，支持 error/empty 细分与 CTA（重试/返回/新建）
- [-] T3 页面层：在 Reader.tsx 接入统一状态机（loading/empty/error/success），移除分散处理
- [-] T4 常量：在 constants/ui.ts 补齐文案映射与测试用 key
- [-] T5 测试：补齐 API/组件/页面的退避、错误映射、a11y 与重试路径（fake timers）
- [-] T6 无障碍：校验 aria、焦点顺序与键盘操作

## 九、完成定义（DoD）

- 所有 AC 通过单测；语义化错误映射稳定；Reader 页面体验一致；无新增 ESLint/TypeScript 错误；CI 通过。

## 十、提交产物

- 源码与测试变更；新建本故事文档；不修改 OpenAPI。

## QA Results

状态：In Progress（阶段性通过，作为新基线入库）

一、与同事审查建议的逐条复核结论（状态更新：均已落地）
- 信号联动降重：已抽取并接入 linkAbortSignals()，实现统一联动与清理。实现见 [apps/frontend/src/api/sse.ts:17-50](apps/frontend/src/api/sse.ts:17-50)，在 [`sendMessage()`](apps/frontend/src/api/client.ts:425-434) 中使用并在 finally 中统一清理。
- SSE 解析抽取：已抽取并接入 parseSSEStream()，承载 UTF-8 解码、事件分割与 JSON 安全解析；坏 JSON 以 { raw } 上报不中断。实现见 [apps/frontend/src/api/sse.ts:59-162](apps/frontend/src/api/sse.ts:59-162)，在 [`sendMessage()`](apps/frontend/src/api/client.ts:460-478) 中使用替换原解析循环。
- 类型安全增强：已定义并应用 SSEEvent 判别联合。类型见 [apps/frontend/src/api/sse.ts:7-11](apps/frontend/src/api/sse.ts:7-11)，在 [`sendMessage()`](apps/frontend/src/api/client.ts:456-478) 以泛型 <StreamData> 约束回调，避免 any 使用。
- 退避与总时限：已实现 500/1000/2000ms 与 10s 总时限；测试已覆盖：[apps/frontend/src/api/client.test.ts:37-76](apps/frontend/src/api/client.test.ts:37-76)、[apps/frontend/src/api/client.test.ts:78-109](apps/frontend/src/api/client.test.ts:78-109)。
- AbortSignal 贯穿：已贯穿 fetch 与重试；调用方中止路径测试覆盖：[apps/frontend/src/api/client.test.ts:255-270](apps/frontend/src/api/client.test.ts:255-270)。

二、风险与后续建议（保持不阻断本故事）
1) 5xx 自适应重试：对 502/503/504 结合 Retry-After 的动态退避已具备雏形，建议在后续 Phase 2 强化策略与测试。
2) 错误日志节流：可按 traceId/time-window 节流重复错误日志，减轻异常时的控制台噪音。

三、测试补齐现状
- SSE 边界：已覆盖 UTF-8 多字节跨 chunk、坏 JSON 容错、最终态收敛（[`client.test.ts`](apps/frontend/src/api/client.test.ts:369-385,308-324)）。
- 信号传播：请求前中止/总时限中止已覆盖（[`client.test.ts`](apps/frontend/src/api/client.test.ts:238-253,255-270)）。
- 常量与 UI：已增补 streaming.loading 与 summary.updated，并由 Reader 消费（[`constants/ui.ts`](apps/frontend/src/constants/ui.ts:53-72)、[`Reader.tsx`](apps/frontend/src/pages/Reader.tsx:420-438,516-521)）。

四、与 1.12 的衔接说明
- 本故事内建议的“解析抽取/类型增强/信号联动降重”已在 1.12 Phase 1 中完成并并入基线；后续 Phase 2 的 5xx 自适应重试与日志节流将在 1.12 或 1.13 承接。

结论
- 维持 In Progress。上述三项高优先级建议已完成并并入现行代码基线，本故事后续工作聚焦 UI/测试细节优化与与 1.12 的回归验证。
