# 1.4 前端：Reader 滚动阅读与本地进度保存（最小闭环）

基于 1.1～1.3 已实现的 Session 创建、获取与 Reader 渲染，本故事聚焦最小闭环：在 Reader 中支持滚动阅读，并将阅读进度（滚动位置）保存至本地 localStorage；再次进入同一 Session 时自动恢复上次阅读位置；离开页面或滚动时适时保存。

## 背景与动机
- 用户在阅读长内容时需要中断与继续阅读能力。
- 当前 Reader 已能渲染内容，但缺少进度保存与恢复，影响连续性体验。
- 采用前端本地存储（localStorage）实现最小闭环，避免后端改动，加速交付。

## 业务目标
- 为每个 Session 维护独立的阅读进度（滚动像素位置或相对百分比）。
- 进入 Reader 时自动恢复对应 Session 的进度。
- 用户滚动或离开页面时保存最新进度。
- 无内容或首次进入时，正常从顶部开始阅读。

## 范围（In Scope）
- 前端 Reader 页面内：
  - 进度键设计：基于 sessionId（例如：`readerProgress:<sessionId>`）
  - 保存与恢复逻辑（localStorage）
  - 边界与错误处理（无权限/无数据/异常键值）
  - 基础 UX 提示（可选，轻量 log 或一次性 toast）
- 无后端变更，无新 API

## 不在范围（Out of Scope）
- 分页模式、目录跳转、服务端进度同步、跨设备同步
- 富提示 UI（如复杂浮层、进度条组件）

## 用户故事
作为一个读者用户，
当我打开某个 Session 的阅读页面时，
系统会恢复到我上次离开时的阅读位置；
当我滚动并离开页面或继续阅读时，
系统会在本地持续保存我的进度，
以便下次回来能从相同位置继续。

## 验收标准（Acceptance Criteria）
1) 进入恢复
   - 前置：localStorage 中存在 key `readerProgress:<sessionId>` 的有效值（数字，像素位置或 0～1 的比例）
   - 当用户通过路由进入 `Reader` 页面并成功渲染内容后
   - 然后系统在首帧可滚动时恢复滚动位置至该值附近（若为比例，按内容高度换算像素）
   - 若无值或为非法值，则从顶部开始

2) 滚动保存
   - 当用户在 Reader 页面滚动时
   - 系统以节流/防抖方式保存最新滚动位置至 localStorage
   - 保存键名包含当前 sessionId，不影响其他 Session
   - 刷新页面后，恢复到保存位置

3) 离开保存
   - 当用户关闭标签、刷新或路由离开时
   - 系统再次保存一次最新位置，避免丢失

4) 兼容与降级
   - 若 localStorage 不可用（隐私模式或异常），系统不报错、可正常阅读但不保存
   - 异常键值自动忽略并从顶部开始

5) 性能与可用性
   - 保存操作对滚动性能无明显影响（建议节流 ≥ 200ms）
   - 在内容初次渲染后 1 秒内完成恢复（可在 layout/paint 后再设置滚动）

6) 可观测性（前端）
   - 控制台日志或轻量埋点：记录一次恢复与首次保存事件（非必须上报，至少可调试）

## 设计要点

- 键名规范
  - `const key = \`readerProgress:${sessionId}\`;`
  - 值格式：建议保存像素位置（number），实现简单稳定；如需比例，保存 `{ type: 'ratio', v: number }`

- 恢复时机
  - 等待内容渲染完成后再恢复。例如使用 `requestAnimationFrame` 或 `setTimeout` 0～100ms
  - 如使用虚拟列表/异步渲染，需要在依赖数据 ready 后恢复

- 保存策略
  - 监听 `scroll` 事件，节流写入 localStorage
  - 在 `beforeunload` 和路由卸载 `useEffect` cleanup 中再保存一次

- 组件范围
  - 建议在 `apps/frontend/src/pages/Reader.tsx` 中实现，复用现有 session 加载与内容渲染逻辑
  - 可将进度读写封装为小型 util 以便单元测试与重用（可选）

## 技术实现草案（示意）

- 进度工具
  - getProgress(sessionId): number | null
  - setProgress(sessionId, value: number): void
  - safeLocalStorage 可用性检测

- Reader 页面改造
  - 从路由/状态读取 sessionId
  - 内容渲染完成后尝试恢复
  - 绑定滚动监听，节流保存
  - 卸载/离开时保存

## 开发任务拆分

- T1 进度存取工具（util）
  - localStorage 可用性探测
  - 键名规范与类型校验
  - 导出 get/set 接口与错误兜底

- T2 Reader 集成（核心）
  - 获取 sessionId
  - 渲染就绪后恢复滚动位置（支持重试 1～2 次以应对延迟渲染）
  - 滚动监听 + 节流保存
  - 卸载时保存

- T3 边界与 UX（可选轻量）
  - 无进度时不提示；首次成功恢复打印一次 debug 日志
  - localStorage 异常静默降级

- T4 基础测试与联调
  - 人工测试路径：
    - 首次进入：正常从顶部开始；滚动后刷新可恢复
    - 切换到其他 Session 互不影响
    - 隐私模式/禁用存储：不报错、不可恢复
  - 单元测试（可选）：util 的键名与序列化

## 验收自检清单（开发完成需自查）
- [x] 使用 sessionId 作为唯一命名空间，未污染其他 key
- [x] 首次加载成功恢复或从顶部开始，不卡顿
- [x] 滚动时节流保存，刷新后恢复有效
- [x] 卸载前保存执行（含 beforeunload 与 visibilitychange 兜底）
- [x] 存储不可用时降级无异常
- [x] 代码中含有适度注释与可调试日志（dev）

## 对 Dumb Dev 的实现指引
- 仅修改前端，不改后端。
- 在 [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1) 中添加滚动进度读取与恢复逻辑；如需抽取工具，可在 [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1) 同级新增 `utils/readingProgress.ts`（新建目录文件）。
- 推荐像素位置实现，避免比例误差；恢复时先 `window.scrollTo(0, y)`，若内容异步增量渲染，使用 `requestAnimationFrame` 连续尝试 2～3 次。
- 节流可使用自写节流函数，避免引入新依赖。
- 注意避免频繁写 localStorage；建议节流 200～500ms。

## 实施补强（本次修复项）
- 新增 visibilitychange 兜底，提升 iOS Safari 等环境下离开页面保存的稳定性
- 恢复期间引入 isRestoring 标记，避免恢复滚动过程中触发保存导致的抖动
- 恢复/保存前对滚动位置进行 clamp，避免超过可滚动上限引起的异常
- 若存在保存进度则不做初始置顶，减少一次无效重绘

## 状态
Done

## QA Results
审查范围与方法：
- 仅依据本故事文档与相关代码入口约定进行静态需求与可测性评审，未修改任何除“QA Results”外的章节与内容。
- 结合前端实现建议与风险点，输出测试结论、缺陷与建议修正项，并给出测试用例清单与验收结论。

结论：
- 需求明确、边界覆盖较全，具备实现与验收可行性。
- 存在少量验收可操作性与实现细节的模糊点，建议在开发合并前修正，详见“发现的问题与建议”。

发现的问题与建议（需在实现/文档中对齐或修正）：
1) 恢复时机与重试窗口需具象化以便验收
   - 文档建议“首帧可滚动时恢复，1 秒内完成，必要时重试 2～3 次”，建议在实现中固定策略，便于测试复现与一致性：
     - 建议策略：首次渲染后 requestAnimationFrame 连续尝试最多 3 次，间隔 50ms；若失败则在 1s 超时前进行一次最终 clamp 后设置。
     - 将该逻辑以常量暴露，便于单测与调参。

2) 进度值规范与序列化约束需要统一
   - 文档同时提到像素 number 与比例对象两种存储，建议本故事限定使用“像素 number”，避免双制式带来解析分支与测试复杂度。
   - 建议：存储与读取均以纯 number；读取时如解析失败或出现非有限数值，视为无效。

3) 滚动保存节流的实现与监听目标需明确
   - 建议明确监听 window 的 scroll 事件（若页面存在内部滚动容器，则应监听容器并以容器 scrollTop 为准，当前故事未引入容器，按 window 处理）。
   - 节流间隔建议固定为 250ms（落在 200～500ms 建议范围中），并在页面隐藏/卸载时强制一次最终保存，避免被节流丢失。

4) clamp 逻辑应以最大可滚动高度为上限
   - 上限应为：document.scrollingElement.scrollHeight - window.innerHeight（或等价计算）。
   - 兼容小于 0 的值统一归零。

5) localStorage 可用性探测与异常处理
   - 建议提供 safeLocalStorage.has/get/set 三元封装：
     - has：返回是否可用（try/catch set/remove）
     - get：catch 异常并返回 null
     - set：catch 异常并 no-op
   - 在不可用时，仅打印一次 debug 日志，避免噪声。

6) 恢复期间 isRestoring 标志与保存抖动
   - 将 isRestoring 期间 suppress 保存；建议 isRestoring 状态仅覆盖恢复尝试窗口（≤ 1s），超过则自动取消，避免极端状态下丢失保存。

7) 可观测性与调试日志
   - 建议仅在 dev 环境输出一次性日志：progress restored / progress saved-first / storage unavailable，以便手测与问题定位。

建议补充的开发验收细化项（对现有 AC 的量化增强）：
- “在内容初次渲染后 1 秒内完成恢复”具象化为：从组件 mount 起计时，≤1000ms 内 scrollTo 生效（可允许再次 attempt）。
- “节流 ≥ 200ms”落地为固定 250ms 节流。
- “异常键值自动忽略并从顶部开始”落地检查：NaN、负数、超过最大可滚动高度、字符串等均视为无效。

建议测试用例清单（手测为主，可补单测 util）：
A. 恢复-正常路径
- A1 保存 1200px，刷新回到 ~1200px（容忍 ±5px）
- A2 切换不同 sessionId 相互独立（key=readerProgress:<sessionId>）

B. 边界与异常
- B1 localStorage 不可用（隐私模式/配额异常）时不报错，可阅读，不保存
- B2 无效值（"abc"、-100、Infinity、超过上限）→ 从顶部开始
- B3 内容高度变化（刷新后内容更长/更短）：恢复值 clamp 到合法范围，不抖动、不报错

C. 事件与时机
- C1 滚动频繁操作时，保存调用次数被节流（采样浏览器性能面板，间隔约 250ms）
- C2 beforeunload/visibilitychange 触发最终保存（可在日志中看到最后一次保存）

D. 性能与体验
- D1 恢复滚动在 1s 内完成，无明显抖动
- D2 isRestoring 期间不触发保存，恢复完成后继续节流保存

E. 可观测性
- E1 控制台出现一次性调试日志（dev），非噪声

建议最小实现结构（便于测试与复用）：
- [`apps/frontend/src/utils/readingProgress.ts`](apps/frontend/src/utils/readingProgress.ts:1)：
  - safeLocalStorage.available(): boolean
  - getProgress(sessionId): number | null
  - setProgress(sessionId, value: number): void
  - clampScroll(value: number): number
- [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)：
  - 在 useEffect 中：恢复尝试（最多 3 次 + 最终兜底，≤1s 窗口，isRestoring 抑制保存）
  - scroll 监听 + 250ms 节流；beforeunload/visibilitychange 最终保存

综合验收结论：
- 本故事“Ready for Review”通过文档评审层面可接受。
- 上述细化项建议在实现 PR 中体现；若实现遵循此处建议策略与阈值，功能可在本地人工测试内稳定通过，风险低。

## 参考
- 相关已有故事：
  - [`1.2.frontend-create-session-and-reader-handoff.md`](docs/stories/1.2.frontend-create-session-and-reader-handoff.md:1)
  - [`1.3.frontend-get-sessions-and-reader-render.md`](docs/stories/1.3.frontend-get-sessions-and-reader-render.md:1)
- 相关代码入口：
  - [`apps/frontend/src/pages/Reader.tsx`](apps/frontend/src/pages/Reader.tsx:1)
  - [`apps/frontend/src/App.tsx`](apps/frontend/src/App.tsx:1)
  - [`apps/frontend/src/main.tsx`](apps/frontend/src/main.tsx:1)
  - [`apps/frontend/src/api/client.ts`](apps/frontend/src/api/client.ts:1)
