# 范围变更单（Scope Change Form）

目的：规范化记录与评估任何对 MVP 范围的增删改，防止范围蔓延（Scope Creep），确保节奏与质量。

关联文档：[`docs/prd.md`](docs/prd.md)｜[`docs/epic-core-mvp.md`](docs/epic-core-mvp.md)｜[`docs/architecture.md`](docs/architecture.md)｜[`docs/ui-ux.md`](docs/ui-ux.md)

---

## 1. 基本信息

- 变更标题（Title）：  
- 提出人（Requester）：  
- 日期（Date）：  
- 关联 Issue/Epic/PR（Links）：  

## 2. 变更类型（选择一项或多项）

- [ ] 增加（Add）
- [ ] 删除（Remove）
- [ ] 修改（Modify）
- [ ] 优先级调整（Reprioritize）
- [ ] 技术替代/重构（Tech Alternative/Refactor）

## 3. 变更内容描述（What）

请清晰描述拟变更的范围与具体内容（功能/接口/交互/文案等）：
- 涉及 FR/NFR：  
- 涉及页面/组件/API：  
- 涉及数据模型/迁移：  

## 4. 变更动因（Why）

说明变更的理由与背景（用户价值/问题/风险/依赖）：
- 用户/业务驱动：  
- 技术/架构驱动：  
- 合规/成本/性能驱动：  

## 5. 影响评估（Impact）

从多维度评估本次变更影响（如无法评估，可标注“待评估”）：
- 用户影响（含体验/可访问性）：  
- 技术影响（FE/BE/DB/CI/CD）：  
- 性能与成本影响（含 LLM 成本/延迟）：  
- 安全与隐私影响：  
- 风险与回滚复杂度：  
- 与现有里程碑/MVP 目标的偏离：  

## 6. 备选方案（Alternatives）

列出至少一个备选方案（如：延后到 Post-MVP、降低规格、灰度实验）：
- 方案A（推荐）：  
- 方案B（备选）：  
- 方案C（延后/弃用）：  

## 7. 工作量与进度（Effort & Timeline）

- 估算工作量（人日）：  
- 涉及角色（PO/UX/FE/BE/QA/DevOps）：  
- 对当前冲刺/里程碑的影响：  
- 预期落地时间：  

## 8. 决策（Decision）

- 决策：APPROVE / CONDITIONAL / REJECT  
- 审批人：  
  - 技术负责人：  
  - 产品负责人（PO）：  
  - UX 负责人（如涉 UI/交互）：  
- 条件（如 CONDITIONAL）：  
- 生效里程碑/版本：  

## 9. 执行与追踪（Execution & Tracking）

- Owner（执行负责人）：  
- 需要创建/更新的工件：  
  - [ ] PRD  
  - [ ] Epic / User Stories  
  - [ ] OpenAPI / 接口文档  
  - [ ] 架构/设计文档  
  - [ ] 前端规范/组件属性  
  - [ ] 测试用例/清单  
- 验收标准（简述）：  
- 回滚预案：  

## 10. 记录与归档

- 最终提交链接（PR/Commit）：  
- 发布记录/Release Notes：  
- 经验沉淀（Lessons Learned）：  

---

使用说明：
- P0/MVP阶段，所有范围新增应优先考虑“降规格/延后”备选方案
- 变更单通过后，需同步更新相应文档与待办，并在每日站会/里程碑评审进行播报