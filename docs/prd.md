# 产品需求文档（PRD）V2.1：知深学习导师

作者：John（Product Manager）  
日期：2025-08-06  
关联文档：项目简报([`docs/brief.md`](docs/brief.md)), 架构([`docs/architecture.md`](docs/architecture.md)), UI/UX([`docs/ui-ux.md`](docs/ui-ux.md))

---

## 0. 执行摘要（Executive Summary）

知深学习导师面向知识工作者与终身学习者，提供“左对话｜右内容”的双栏式深度学习体验，通过“动态上下文管理 + 可交互摘要”帮助用户将外部信息转化为结构化知识，解决通用AI聊天的上下文易丢失、浅层与不可追溯问题。MVP以“粘贴文本→对话学习→自动保存/恢复”为核心闭环，采用 Vite+React + FastAPI + Supabase 的混合架构以快速验证，同时保持可迁移与可扩展能力。

核心成功指标（MVP）：次日留存≥35%；单会话有效对话轮次≥6；摘要交互出现场次≥50%；P50 TTFB<1s、P50 AI响应<3s。

---

## 1. 目标与非目标（Goals & Non-Goals）

1.1 目标  
- 深度理解：围绕特定材料，支持有记忆的连续对话，形成逐步完善的知识结构。  
- 高效体验：极简双栏界面、流式响应、自动保存与断点续学。  
- 可追溯性：构建“摘要 ↔ 对话 ↔ 原文”的可验证闭环（MVP先实现摘要→对话，后MVP补齐对话→原文链路）。  
- 可演进：以适配层抽象外部服务（LLM、DB、存储、Auth），为后续迁移与扩展留足空间。

1.2 非目标（MVP阶段不覆盖）  
- 多格式导入（PDF、URL、视频等一键抓取清洗）。  
- 记忆熔炉（间隔重复、卡片体系）与跨文档知识图谱。  
- 全语音交互/播客模式。  
- 社交分享、协作、多账户角色体系等扩展能力。

---

## 2. 背景与问题陈述（Background & Problem Statement）

用户不缺内容，缺的是将复杂文本“深度理解与内化”的有效机制。通用AI聊天上下文粘性弱、易漂移；稍后读/阅读器偏重收集；笔记工具依赖用户自我组织。结果是认知负荷高、理解浅、难以追溯与验证。

知深学习导师通过“动态上下文管理”和“可交互摘要”，把注意力锁定在当前材料及其相关概念，持续积累对话上下文，以结构化的可交互摘要作为认知支点，帮助用户由“熟知”走向“真知”。

---

## 3. 用户与场景（Users & Use Cases）

3.1 目标用户  
- 知识工作者：研报、论文、法律与技术文档的深度理解与应用。  
- 学生/终身学习者：系统性课程、书籍与复杂议题的内化。

3.2 典型场景  
- 长文深读：围绕一篇长文进行结构化提问、澄清概念与反思。  
- 专题钻研：多轮交互逐步搭建个人理解图谱。  
- 断点续学：跨时段回到原进度，延续对话与摘要状态。

---

## 4. 范围（Scope）

4.1 In Scope（MVP）  
- 纯文本粘贴导入创建学习会话。  
- 双栏学习界面（左对话、右内容画布“原文/摘要”切换）。  
- 动态摘要的最小可用生成与更新；摘要节点基本交互（展开/定位到对应对话）。  
- 自动保存与断点续学（会话、消息、摘要、阅读位置）。  
- 统一错误模型、基础可观测性、基础重试与超时策略。

4.2 Out of Scope（MVP）  
- 多格式导入（PDF、URL、视频）、多来源抓取与清洗。  
- 间隔重复/卡片生成、跨文档链接、知识地图。  
- 语音学习模式、播客摘要。  
- 协作分享、多端同步策略（除浏览器端基本持久化外）。

---

## 5. 需求（Requirements）

5.1 功能性需求（FR）  
- FR1 会话创建：用户将纯文本粘贴到输入区域，点击提交后创建学习会话。  
- FR2 内容处理：系统接收并持久化文本，生成会话ID与元信息（标题、创建时间）。  
- FR3 核心界面：提供双栏布局；左栏为对话历史与输入框；右栏为内容画布。  
- FR4 对话交互：用户在左栏输入问题或指令，系统返回AI回答（流式优先）。  
- FR5 聚焦上下文：AI回答基于当前材料与摘要核心，减少离题与幻觉。  
- FR6 动态摘要：对话过程中生成或更新“动态摘要”，反映核心概念与用户理解进展。  
- FR7 视图切换：右栏可在“原文/摘要”视图之间切换，默认态可配置。  
- FR8 进度自动保存：会话、消息、摘要、阅读位置自动持久化。  
- FR9 断点续学：回到知识库后选中历史会话，恢复上次学习状态。

5.2 非功能性需求（NFR）  
- NFR1 平台兼容：现代桌面浏览器一致性（Chrome/Firefox/Safari/Edge 最新版）。  
- NFR2 性能：P50 首屏加载<2s；P50 AI响应<3s；P50 TTFB<1s（流式）。  
- NFR3 可靠性：数据持久；服务可用性目标≥99.5%。  
- NFR4 安全性：传输与静态加密；RLS策略；输入严格校验。  
- NFR5 成本效益：请求合并、流式响应、缓存策略、预算告警。

5.3 验收标准（高层）  
- 创建会话返回ID；可进入阅读视图与学习会话视图；多轮对话稳定运行；摘要自动更新；状态可恢复。  
- 错误返回统一模型；基础观测指标可见；异常有可追溯 trace_id。

提示：详细验收标准见第9章“里程碑与交付清单”和第10章“验收标准”。

---

## 6. 信息架构与关键流程（IA & Flows）

6.1 视图与信息结构  
- 知识库（Library）：三栏布局；左（导航/标签）、中（列表）、右（预览/阅读/学习）。  
- 学习会话（Session）：双栏布局；左（对话）、右（内容画布：原文/摘要）。  
- 阅读视图（Reader）：单栏，为预阅读与高亮优化。

6.2 关键流程（基于[`docs/ui-ux.md`](docs/ui-ux.md) 3.1/3.2）  
- 流程A：粘贴文本→创建会话→阅读视图→“与导师学习”→学习会话（双栏）。  
- 流程B：历史会话→恢复阅读/学习状态→继续对话与摘要更新。

---

## 7. 设计与用户体验（Design & UX）

7.1 设计原则  
- 专注与沉浸：减少干扰，突出对话与内容。  
- 强大且直观：功能强，但通过渐进式披露保持简单。  
- 可追溯与可信：摘要结构清晰，可回溯对应对话与原文（后MVP补齐完整链路）。  
- 可访问性：遵循WCAG 2.1 AA目标。

7.2 关键组件（节选）  
- Sidebar Nav、Document List Item、Filter Controls、内容画布切换Tabs、对话输入与历史、摘要节点组件（可展开/定位）。  
- 设计系统与动效：保持简洁、专业；适当过渡提升流畅感。

---

## 8. 技术假设与架构对齐（Tech Assumptions & Alignment）

- 前端：Vite+React SPA；统一API客户端；错误/重试策略；trace_id注入。  
- 后端：FastAPI 单体网关/编排；适配器层（DB/Storage/LLM/Auth）；Repository 模式隔离数据访问。  
- 基建：Supabase（PostgreSQL/Auth/Storage）；未来可替换为自建或国内云。  
- 部署：前端 Vercel/Netlify；后端 Render/Fly.io；CI/CD GitHub Actions。  
- 可观测性：Sentry、结构化日志、链路追踪（trace_id）。  
- 详见架构文档（[`docs/architecture.md`](docs/architecture.md)）。

---

## 9. 里程碑与交付清单（Milestones & Deliverables）

阶段A（MVP，0-6周）  
- M1 项目骨架：前端/后端初始化；CI 基线；Sentry接入；.env.example。  
- M2 FR1/2：POST /api/sessions；DB表 sessions 建立；前端粘贴导入创建会话。  
- M3 FR3/7：双栏界面与右栏“原文/摘要”切换；阅读视图到学习会话平滑过渡。  
- M4 FR4/5/6：对话交互与动态摘要最小可用版本；基于材料的聚焦回答。  
- M5 FR8/9：自动保存与断点续学；知识库列表与历史会话恢复。  
- M6 NFR 基线：性能优化（流式响应）、重试超时、速率限制；统一错误模型。  
- M7 验收与发布：对照验收标准完成检查，发布MVP。

阶段B（Post-MVP，6-12周）  
- B1 多格式导入（URL/PDF）与初步清洗。  
- B2 摘要↔对话↔原文三向追溯闭环。  
- B3 记忆熔炉（卡片/间隔重复）试验版与可用性验证。

阶段C（演进，12周+）  
- C1 知识网络化与跨文档连接。  
- C2 语音学习模式（播客 + 纯语音交互）。  
- C3 成本/可靠性深度优化与A/B体系完善。

---

## 10. 验收标准（Acceptance Criteria）

功能验收  
- 创建会话：粘贴文本→返回会话ID→列表可见→右栏可进入阅读视图。  
- 学习会话：从阅读视图点击“与导师学习”→双栏模式→可流式对话→摘要自动更新。  
- 切换视图：右栏可在原文/摘要切换，记住用户选择或根据策略默认。  
- 持久化：刷新/重登后，从知识库进入历史会话可完整恢复对话、摘要、阅读位置。  
- 错误模型：任一失败请求返回统一错误结构（含 trace_id），UI有用户可理解提示。

性能与可靠性  
- P50 首屏加载<2s；P50 AI响应<3s；P50 TTFB<1s。  
- 正常网络条件下，对话失败率低于2%；客户端重试生效并有指数退避。  
- 日志结构化并含 trace_id；Sentry 收集前后端错误。

可用性  
- 首次使用引导完成率≥70%；核心任务（创建会话并完成5轮有效对话）成功率≥80%。

---

## 11. 指标与监测（Metrics & Monitoring）

北极星指标  
- 每周深度学习时长（含有效对话轮次、摘要交互次数）。

核心产品指标  
- 次日留存（激活会话后）≥35%。  
- 单会话平均有效对话轮次≥6。  
- 摘要交互使用率≥50% 的会话出现。  
- 历史会话恢复率与恢复后的继续学习时长。

技术与运维指标  
- API 请求率、错误率、延迟（P50/P90/P99）。  
- LLM 调用次数、Token 消耗、延迟、错误率。  
- 成本预算告警触发次数与处置时长。

---

## 12. 风险与缓解（Risks & Mitigations）

- 技术风险（高）：动态摘要质量波动 → 最小可解释结构上线；灰度优化提示策略；提供“一键回到原文”。  
- 成本风险（高）：LLM 调用成本不确定 → 请求合并/缓存/速率限制；预算告警；分层模型策略。  
- 竞争风险（中）：同类产品快速迭代 → 以“深度与可追溯”构筑差异；加速MVP节奏。  
- 接受度风险（中）：深度学习门槛 → 轻量模式与引导模板，降低上手成本。

---

## 13. 依赖、假设与约束（Dependencies, Assumptions & Constraints）

依赖  
- 外部：LLM Provider（可插拔）、Supabase（PG/Auth/Storage）、Sentry、Vercel/Render等部署平台。  
- 团队：PM/BA、UX、FE、BE、QA（可兼职组合）。

假设  
- 现有LLM能力可通过工程化实现“动态上下文管理 + 动态摘要”的最小可用体验。  
- 目标用户认可“深度对话式学习”的价值，并愿意投入时间与注意力。  
- 浏览器端为首要平台，桌面端优先优化。

约束  
- MVP资源有限，严格控制范围（见第4章）。  
- 遵从统一错误模型、观测与安全基线；遵循隐私与合规要求。

---

## 14. 可观测性与运维（Observability & Operations）

- 日志：后端结构化JSON日志，包含 trace_id、时间戳、级别、上下文。  
- 监控：API请求率/错误率/延迟，LLM成本与延迟；平台与Supabase仪表盘。  
- 告警：API错误率、P99延迟、LLM预算阈值；通过Slack通知。  
- 追踪：前端、后端与错误报告贯穿 trace_id。  
- CI/CD：按[`docs/architecture.md`](docs/architecture.md) 7.2的GitHub Actions工作流执行。

---

## 15. 附录（Appendix）

15.1 统一错误响应模型（参考）  
```json
{
  "error": {
    "code": "UNIQUE_ERROR_CODE",
    "message": "对用户友好的错误信息。",
    "details": {
      "field": "specific_field_if_any",
      "reason": "technical_reason_for_the_error"
    },
    "trace_id": "request_trace_uuid"
  }
}
```

15.2 环境变量模板（节选，详见架构文档12.1）  
- SUPABASE_URL / ANON_KEY / SERVICE_ROLE_KEY  
- DATABASE_URL  
- LLM_API_KEY / LLM_API_BASE  
- SENTRY_DSN

15.3 相关文档  
- 项目简报（完整版）：[`docs/brief.md`](docs/brief.md)  
- 全栈架构文档 V2.1：[`docs/architecture.md`](docs/architecture.md)  
- UI/UX规格说明书 V2.0：[`docs/ui-ux.md`](docs/ui-ux.md)

（PRD V2.1 完）