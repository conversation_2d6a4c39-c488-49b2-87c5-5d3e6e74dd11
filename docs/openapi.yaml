openapi: 3.0.3
info:
  title: Zhi Deep Learning Mentor API
  version: 0.0.1
  description: |
    MVP OpenAPI v0 草案。覆盖核心会话创建、会话详情与消息发送（含流式说明）。
    统一错误模型与 trace_id 见 schemas.ErrorResponse。
servers:
  - url: https://api.example.com
    description: 生产环境
  - url: http://localhost:8000
    description: 本地开发

tags:
  - name: Sessions
    description: 学习会话管理
  - name: Messages
    description: 会话内消息（对话）

paths:
  /api/sessions:
    post:
      tags: [Sessions]
      summary: 创建学习会话（由纯文本材料）
      description: |
        从用户粘贴的纯文本创建会话。服务端持久化文本并返回会话元信息。
      operationId: createSession
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSessionRequest'
      responses:
        '201':
          description: 已创建
          headers:
            X-Trace-Id:
              schema: { type: string }
              description: 请求链路追踪ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionSummary'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '500':
          $ref: '#/components/responses/ServerError'
    get:
      tags: [Sessions]
      summary: 获取用户会话列表
      description: 获取当前用户的所有学习会话，按创建时间降序排列
      operationId: listSessions
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: limit
          schema: { type: integer, default: 50, minimum: 0, maximum: 50 }
          description: 返回的最大条数，上限为 50；若超出将被截断为 50
        - in: query
          name: offset
          schema: { type: integer, default: 0, minimum: 0 }
      responses:
        '200':
          description: OK
          headers:
            X-Trace-Id:
              schema: { type: string }
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/sessions/{id}:
    get:
      tags: [Sessions]
      summary: 获取会话详情（含最小 Reader 内容）
      description: |
        根据会话ID获取会话详细信息（含用于 Reader 的最小 content.paragraphs）。
        注意：当前 content.paragraphs 为“示例内容（sample）”，后续将替换为真实数据源。
      operationId: getSession
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: OK
          headers:
            X-Trace-Id:
              schema: { type: string }
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionDetailResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'
  /api/sessions/{id}/progress:
    get:
      tags: [Sessions]
      summary: 获取会话阅读进度（百分比）
      description: 读取指定会话的阅读进度 progress（0-100），并返回 meta（updatedAt/version/lastSource）
      operationId: getSessionProgress
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: OK
          headers:
            X-Trace-Id:
              schema: { type: string }
            ETag:
              schema: { type: string }
              description: 当前进度版本（用于 If-Match）
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionProgressGetResponse'
              examples:
                success:
                  value:
                    session_id: "11111111-**************-************"
                    progress: 42
                    meta:
                      updatedAt: "2025-08-06T09:00:00Z"
                      version: "8d2a6c65-2b3d-4fd0-8a0c-0a2f6b5e7d11"
                      lastSource:
                        deviceId: "dev-abc123"
                        userAgent: "Mozilla/5.0 ..."
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/ServerError'
    put:
      tags: [Sessions]
      summary: 更新会话阅读进度（百分比）
      description: 将 progressPercent 写入 sessions.reading_state 中，使用 ETag/If-Match 并发控制
      operationId: updateSessionProgress
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: string, format: uuid }
        - name: If-Match
          in: header
          required: true
          schema: { type: string }
          description: 上次 GET 返回的 ETag（进度版本）
        - name: X-Device-Id
          in: header
          required: false
          schema: { type: string }
          description: 客户端生成并持久化的设备标识
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionProgressUpdateRequest'
      responses:
        '200':
          description: OK
          headers:
            X-Trace-Id:
              schema: { type: string }
            ETag:
              schema: { type: string }
              description: 新的进度版本
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionProgressGetResponse'
              examples:
                success:
                  value:
                    session_id: "11111111-**************-************"
                    progress: 45
                    meta:
                      updatedAt: "2025-08-06T09:00:33Z"
                      version: "a1b2c3d4-1111-**************55556666"
                      lastSource:
                        deviceId: "dev-abc123"
                        userAgent: "Mozilla/5.0 ..."
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: 版本冲突
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SessionProgressConflict'
              examples:
                conflict:
                  value:
                    error: "conflict"
                    server:
                      offset: 48
                      updatedAt: "2025-08-06T09:00:29Z"
                      version: "8d2a6c65-2b3d-4fd0-8a0c-0a2f6b5e7d11"
                      lastSource:
                        deviceId: "dev-xyz789"
                        userAgent: "Mozilla/5.0 ..."
        '428':
          description: 需要前置条件（缺少 If-Match）
          content:
            application/json:
              schema:
                type: object
                properties:
                  error:
                    type: string
                    example: precondition_required
                  message:
                    type: string
                    example: If-Match header required for concurrency control
        '500':
          $ref: '#/components/responses/ServerError'
  /api/sessions/{id}/messages:
    post:
      tags: [Messages]
      summary: 在会话中发送一条用户消息并获取AI回复（支持流式）
      description: |
        推荐以 text/event-stream 流式返回（SSE），也可在不支持流的环境返回最终整包响应。
        - SSE 通道事件：
          - event: token / data: 部分文本
          - event: summary_update / data: Summary
          - event: done / data: { message, summary? }
        - 非流式：返回最终 { message, summary? }
      operationId: postMessage
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema: { type: string, format: uuid }
        - name: stream
          in: query
          required: false
          schema: { type: boolean, default: true }
          description: 是否以 SSE 流式返回
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostMessageRequest'
      responses:
        '200':
          description: OK（非流式）
          headers:
            X-Trace-Id:
              schema: { type: string }
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostMessageResult'
        '202':
          description: SSE 流式开始（当使用流式通道时）
          headers:
            Content-Type:
              schema:
                type: string
                example: text/event-stream
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '500':
          $ref: '#/components/responses/ServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  responses:
    BadRequest:
      description: 请求无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: 未认证或无效令牌
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    TooManyRequests:
      description: 触发速率限制
      headers:
        Retry-After:
          schema: { type: integer, format: int32 }
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    ServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  schemas:
    CreateSessionRequest:
      type: object
      required: [text]
      properties:
        text:
          type: string
          minLength: 1
          description: 用户粘贴的纯文本材料
    SessionSummary:
      type: object
      required: [id, title, created_at]
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
        created_at:
          type: string
          format: date-time
        preview:
          type: string
          description: 可选的摘要预览字段
    SessionListResponse:
      type: object
      required: [items, total]
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/SessionSummary'
        total:
          type: integer
    SessionParagraph:
      type: object
      required: [index, text]
      properties:
        index: { type: integer }
        text: { type: string }
    SessionContent:
      type: object
      required: [language, source, paragraphs]
      properties:
        language: { type: string, example: en }
        source:
          type: string
          example: sample
          enum: [upload, url, sample]
          description: 当前为 sample（示例数据），用于最小可演示闭环；后续版本将输出真实来源
        paragraphs:
          type: array
          items:
            $ref: '#/components/schemas/SessionParagraph'
    SessionDetailResponse:
      allOf:
        - $ref: '#/components/schemas/SessionSummary'
        - type: object
          required: [content]
          properties:
            content:
              $ref: '#/components/schemas/SessionContent'
    Message:
      type: object
      required: [id, role, content, created_at]
      properties:
        id: { type: string, format: uuid }
        role: { type: string, enum: [user, assistant] }
        content: { type: string }
        created_at: { type: string, format: date-time }
    SummaryNode:
      type: object
      properties:
        id: { type: string }
        title: { type: string }
        bullets:
          type: array
          items: { type: string }
        linkedMessageIds:
          type: array
          items: { type: string, format: uuid }
    Summary:
      type: object
      required: [id, session_id, version, text, created_at]
      properties:
        id: { type: string, format: uuid }
        session_id: { type: string, format: uuid }
        version: { type: integer }
        text: { type: string }
        structured_content:
          type: object
          properties:
            sections:
              type: array
              items:
                type: object
                properties:
                  id: { type: string }
                  title: { type: string }
                  bullets:
                    type: array
                    items: { type: string }
                  linkedMessageIds:
                    type: array
                    items: { type: string, format: uuid }
        created_at: { type: string, format: date-time }
    SessionDetail:
      type: object
      required: [id, title, created_at, messages]
      properties:
        id: { type: string, format: uuid }
        title: { type: string }
        created_at: { type: string, format: date-time }
        messages:
          type: array
          items:
            $ref: '#/components/schemas/Message'
        summary:
          $ref: '#/components/schemas/Summary'
        reading_position:
          type: integer
          description: 可选，阅读位置或内容索引
    PostMessageRequest:
      type: object
      required: [content]
      properties:
        content:
          type: string
          minLength: 1
          description: 用户输入的对话消息
    PostMessageResult:
      type: object
      required: [message]
      properties:
        message:
          $ref: '#/components/schemas/Message'
        summary:
          $ref: '#/components/schemas/Summary'
    ErrorResponse:
      type: object
      required: [error]
      properties:
        error:
          type: object
          required: [code, message, trace_id]
          properties:
            code: { type: string }
            message: { type: string }
            details:
              type: object
              additionalProperties: true
            trace_id: { type: string }
    SessionProgressResponse:
      type: object
      required: [session_id, progress]
      properties:
        session_id:
          type: string
          format: uuid
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: 阅读进度百分比（0-100）
    SessionProgressGetResponse:
      allOf:
        - $ref: '#/components/schemas/SessionProgressResponse'
        - type: object
          properties:
            meta:
              type: object
              properties:
                updatedAt:
                  type: string
                  format: date-time
                  description: UTC ISO8601（Z）
                version:
                  type: string
                  description: 进度版本（对应 ETag）
                lastSource:
                  type: object
                  properties:
                    deviceId: { type: string }
                    userAgent: { type: string }
    SessionProgressUpdateRequest:
      type: object
      required: [progress]
      properties:
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: 阅读进度百分比（0-100）