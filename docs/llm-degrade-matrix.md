# LLM 降级与回退策略表（MVP）

目的：在成本异常、限速/配额触发、延迟升高或提供商故障时，提供明确的降级与回退动作，确保关键用户路径可用并可感知。

关联文档：[`docs/architecture.md`](docs/architecture.md)｜[`docs/prd.md`](docs/prd.md)｜[`docs/ui-ux.md`](docs/ui-ux.md)｜[`docs/openapi.yaml`](docs/openapi.yaml)

---

## 1. 触发条件与监控指标

监控来源：后端指标、Sentry、LLM 适配层统计、平台告警

主要阈值（建议初值，按环境调整）：
- 成本预算：本月累计成本 ≥ 预算的 80%（预警）/ 95%（强制降级）
- 速率限制：429 比例 ≥ 2%（5分钟窗口）
- 延迟：P50 > 3s 或 P99 > 12s（连续 5 分钟）
- 错误率：5xx/LLM错误率 ≥ 2%（5分钟窗口）
- 超时：外部请求超时率 ≥ 2%

---

## 2. 降级矩阵（按优先级）

| 场景 | 触发条件 | 动作 | 用户可见反馈 | 回退检查点 |
| --- | --- | --- | --- | --- |
| 模型降级 | 成本≥80% 或 P99延迟高 | 将默认模型切至“性价比模型”，精度提示模板简化 | “系统已切换至稳定模式，响应更快但可能更简略。” | 成本/延迟恢复至阈值内 30 分钟 |
| 采样参数收敛 | 错误/幻觉上升 | 降低 temperature/top_p，启用更保守提示 | “已启用稳健回答策略，优先确保准确性。” | 幻觉/错误率回落 |
| 摘要频率降级 | 成本/延迟/限速 | 将“每条消息触发摘要更新”改为“每N条/累计字数阈值触发” | “摘要将在对话稳定后更新，您可稍后查看变化。” | 会话端指标平稳 |
| 结果缓存启用 | 命中相似问题高 | 启用非个性化缓存（key: prompt hash + 模型版本） | “为提升速度，部分回答来自缓存并已校验。” | 命中率/新鲜度策略达标 |
| 并发/队列限流 | 429/延迟高 | 入口限流与排队提示，重试退避 | “用户较多，已为您排队，约X秒后开始。” | 429 比例回落 |
| 功能层级降级 | 提供商不可用 | 暂停动态摘要，仅保留对话；或切至备选提供商 | “当前使用简化模式，摘要稍后补全。” | 主提供商恢复可用 |

注：降级动作组合执行，优先级从上至下。每步动作需记录日志（含 trace_id）。

---

## 3. 提示模板降级（示例）

正常版（摘要更新）：
- 指令：提取主题/要点，链接到最近K条对话 messageIds，尽量简洁但保持可追溯性。

稳定版（降级）：
- 指令：仅输出层级结构（H1/H2），省略细节，最多 N 条要点；不做跨条关联；保留上版结构尽量不变。

---

## 4. 用户体验与文案

- Loading：显示“稳定模式提示”徽标与 Tooltips
- Summary Tab：显示“已延后更新”轻量提示，允许用户手动触发一次更新（速率限制内）
- 错误气泡：统一错误模型，提供“复制诊断信息”和“重试”按钮

---

## 5. 运维与开关

- 配置项（读取自环境变量/后端配置服务）：
  - LLM_DEFAULT_MODEL / LLM_FALLBACK_MODEL
  - LLM_ENABLE_CACHING（布尔）
  - SUMMARY_UPDATE_INTERVAL (消息数阈值) / SUMMARY_UPDATE_CHAR_THRESHOLD
  - RETRY_BACKOFF_* / EXTERNAL_MAX_RETRIES
- 灰度：按用户/会话进行百分比灰度，记录样本对照
- 审计：每次降级/回退事件写审计日志（时间、触发、动作、影响范围、trace_id 样例）

---

## 6. 演练（GameDay）

- 每月一次：手动触发降级开关并验证关键路径（创建会话、对话、摘要查看）可用
- 验证项：指标告警、用户反馈文案、回退成功率、Sentry 事件质量

（文档v0）